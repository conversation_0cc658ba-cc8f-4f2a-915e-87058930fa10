/**
 * @file offb_node.cpp
 * @brief Offboard control example node, written with MAVROS version 0.19.x, PX4 Pro Flight
 * Stack and tested in Gazebo SITL
 */

#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <mavros_msgs/CommandBool.h>
#include <mavros_msgs/SetMode.h>
#include <mavros_msgs/State.h>
#include <termios.h>
#include <stdio.h>
#include <sys/select.h>
#include <sys/time.h>

// 键盘控制相关变量
float linear_speed = 50.0;  // 线速度
float angular_speed = 1.0; // 角速度

// 获取键盘输入（非阻塞且无回显）
int getKey() {
    int ch = 0;
    struct termios oldt, newt;
    struct timeval tv;
    fd_set rfds;
    
    // 保存当前终端参数
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    
    // 禁用回显和规范模式
    newt.c_lflag &= ~(ICANON | ECHO | ISIG);
    
    // 设置新的终端参数
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    
    // 设置select参数
    FD_ZERO(&rfds);
    FD_SET(STDIN_FILENO, &rfds);
    tv.tv_sec = 0;
    tv.tv_usec = 10000;  // 10ms超时
    
    // 检查是否有输入
    if (select(STDIN_FILENO + 1, &rfds, NULL, NULL, &tv) > 0) {
        ch = getchar();
    }
    
    // 恢复原始终端参数
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    
    return ch;
}

mavros_msgs::State current_state;
void state_cb(const mavros_msgs::State::ConstPtr& msg){
    current_state = *msg;
}

int main(int argc, char **argv)
{
    ros::init(argc, argv, "keyboard_control_node");
    ros::NodeHandle nh;

    // 订阅状态
    ros::Subscriber state_sub = nh.subscribe<mavros_msgs::State>
            ("/uav1/mavros/state", 10, state_cb);
            
    // 发布速度控制
    ros::Publisher vel_pub = nh.advertise<geometry_msgs::TwistStamped>
            ("/uav1/mavros/setpoint_velocity/cmd_vel", 10);
            
    // 服务客户端
    ros::ServiceClient arming_client = nh.serviceClient<mavros_msgs::CommandBool>
            ("/uav1/mavros/cmd/arming");
    ros::ServiceClient set_mode_client = nh.serviceClient<mavros_msgs::SetMode>
            ("/uav1/mavros/set_mode");

    // 控制频率
    ros::Rate rate(20.0);

    // 等待FCU连接
    while(ros::ok() && !current_state.connected){
        ros::spinOnce();
        rate.sleep();
    }
    ROS_INFO("FCU connected");

    // 初始化速度消息
    geometry_msgs::TwistStamped vel;
    vel.header.frame_id = "map";
    vel.twist.linear.x = 0;
    vel.twist.linear.y = 0;
    vel.twist.linear.z = 0;
    vel.twist.angular.x = 0;
    vel.twist.angular.y = 0;
    vel.twist.angular.z = 0;

    // 发送一些初始速度指令
    for(int i = 100; ros::ok() && i > 0; --i){
        vel_pub.publish(vel);
        ros::spinOnce();
        rate.sleep();
    }

    // 设置模式和服务
    mavros_msgs::SetMode offb_set_mode;
    offb_set_mode.request.custom_mode = "OFFBOARD";

    mavros_msgs::CommandBool arm_cmd;
    arm_cmd.request.value = true;

    ros::Time last_request = ros::Time::now();
    ros::Time last_key = ros::Time::now();
    

    // 打印控制说明
    ROS_INFO("README");
    // ROS_INFO("w/s: 前进/后退");
    // ROS_INFO("a/d: 左移/右移");
    // ROS_INFO("q/e: 上升/下降");
    // ROS_INFO("j/l: 左转/右转");
    // ROS_INFO("k: 停止");
    // ROS_INFO("Ctrl+C: 退出");

    // 添加按键状态记录变量
    bool key_w = false, key_s = false;
    bool key_a = false, key_d = false;
    bool key_q = false, key_e = false;
    bool key_j = false, key_l = false;

    while(ros::ok()){
        int key = getKey();
        if(key != 0) {
            // 按键处理代码
            switch(key) {
                case 'w':
                    vel.twist.linear.x = linear_speed;
                    break;
                case 's':
                    vel.twist.linear.x = -linear_speed;
                    break;
                case 'a':
                    vel.twist.linear.y = linear_speed;
                    break;
                case 'd':
                    vel.twist.linear.y = -linear_speed;
                    break;
                case 'q':
                    vel.twist.linear.z = linear_speed;
                    break;
                case 'e':
                    vel.twist.linear.z = -linear_speed;
                    break;
                case 'j':
                    vel.twist.angular.z = angular_speed;
                    break;
                case 'l':
                    vel.twist.angular.z = -angular_speed;
                    break;
                case 'k':
                    // 停止所有运动
                    vel.twist.linear.x = 0;
                    vel.twist.linear.y = 0;
                    vel.twist.linear.z = 0;
                    vel.twist.angular.z = 0;
                    break;
                default:
                    break;
            }
            last_key = ros::Time::now();
        } else {
            if (ros::Time::now()-last_key>ros::Duration(1.0))
            {
                // 无按键时检查并归零速度
                vel.twist.linear.x = 0;
                vel.twist.linear.y = 0;
                vel.twist.linear.z = 0;
                vel.twist.angular.z = 0;
            }          
        }
        
        // 设置OFFBOARD模式
        if(current_state.mode != "OFFBOARD" &&
           (ros::Time::now() - last_request > ros::Duration(5.0))){
            if(set_mode_client.call(offb_set_mode) &&
               offb_set_mode.response.mode_sent){
                ROS_INFO("Offboard enabled");
            }
            last_request = ros::Time::now();
        } else {
            // 解锁
            if(!current_state.armed &&
               (ros::Time::now() - last_request > ros::Duration(5.0))){
                if(arming_client.call(arm_cmd) &&
                   arm_cmd.response.success){
                    ROS_INFO("Vehicle armed");
                }
                last_request = ros::Time::now();
            }
        }

        // 发布速度指令
        vel.header.stamp = ros::Time::now();
        vel_pub.publish(vel);

        ros::spinOnce();
        rate.sleep();
    }

    return 0;
}
