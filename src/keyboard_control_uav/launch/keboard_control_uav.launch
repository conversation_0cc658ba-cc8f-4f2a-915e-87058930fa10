<launch>
    <arg name="velocity_topic" default="/uav1/mavros/setpoint_attitude/cmd_vel"/>
    <arg name="state_topic" default="/uav1/mavros/state"/>
    <arg name="arming_service" default="/uav1/mavros/cmd/arming"/>
    <arg name="set_mode_service" default="/uav1/mavros/set_mode"/>
    <arg name="linear_speed" default="1.0"/>
    <arg name="angular_speed" default="1.0"/>

    <node name="keyboard_control_uav_node" pkg="keyboard_control_uav" type="keyboard_control_uav_node" output="screen">
        <param name="velocity_topic" value="$(arg velocity_topic)"/>
        <param name="state_topic" value="$(arg state_topic)"/>
        <param name="arming_service" value="$(arg arming_service)"/>
        <param name="set_mode_service" value="$(arg set_mode_service)"/>
        <param name="linear_speed" value="$(arg linear_speed)"/>
        <param name="angular_speed" value="$(arg angular_speed)"/>
    </node>
</launch>