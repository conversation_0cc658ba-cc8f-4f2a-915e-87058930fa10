#pragma once

#include <memory>

namespace px4_tracker_control {

class PIDController {
public:
    explicit PIDController(double kp, double ki, double kd, double output_limit);
    
    // 计算PID输出
    double compute(double error);
    
    // 重置积分项
    void reset();
    
    // 设置PID参数
    void setParameters(double kp, double ki, double kd);
    
private:
    double kp_;           // 比例系数
    double ki_;           // 积分系数
    double kd_;           // 微分系数
    double output_limit_; // 输出限幅
    
    double integral_;     // 积分项
    double last_error_;  // 上一次误差
};

} // namespace px4_tracker_control 