#pragma once

#include <memory>
#include <cmath>
#include <Eigen/Dense>

namespace px4_tracker_control {

class IBVSController {
public:
    /**
     * @brief 构造函数
     * @param lambda_gain 控制增益
     * @param velocity_limit 速度限制
     * @param error_threshold 误差阈值
     * @param damping_factor 阻尼因子
     * @param max_rotation_rate 最大旋转速率
     * @param focal_length_x x方向相机焦距
     * @param focal_length_y y方向相机焦距
     */
    explicit IBVSController(double lambda_gain = 0.2,
                           double velocity_limit = 0.5,
                           double error_threshold = 10.0,
                           double damping_factor = 0.3,
                           double max_rotation_rate = 0.5,
                           double focal_length_x = 531.15,
                           double focal_length_y = 531.15);

    /**
     * @brief 完整的IBVS控制算法
     * @param delta_x 目标在图像中与中心的x方向偏差（像素）
     * @param delta_y 目标在图像中与中心的y方向偏差（像素）
     * @param depth 目标的深度（米）
     * @return 无人机的速度控制指令 (vx, vy, vz, yaw_rate)
     */
    std::tuple<double, double, double, double> compute(double delta_x, double delta_y, double depth = 1.0);

    /**
     * @brief 简化版的IBVS控制算法，使用比例控制
     * @param delta_x 目标在图像中与中心的x方向偏差（像素）
     * @param delta_y 目标在图像中与中心的y方向偏差（像素）
     * @param depth 目标的深度（米）
     * @return 无人机的速度控制指令 (vx, vy, vz, yaw_rate)
     */
    std::tuple<double, double, double, double> computeSimple(double delta_x, double delta_y, double depth = 1.0);

    /**
     * @brief 设置控制参数
     * @param lambda_gain 控制增益
     * @param velocity_limit 速度限制
     * @param error_threshold 误差阈值
     * @param damping_factor 阻尼因子
     * @param max_rotation_rate 最大旋转速率
     */
    void setParameters(double lambda_gain, double velocity_limit, double error_threshold,
                      double damping_factor, double max_rotation_rate);

private:
    double lambda_gain_;       // 控制增益
    double velocity_limit_;    // 速度限制
    double error_threshold_;   // 误差阈值
    double damping_factor_;    // 阻尼因子
    double max_rotation_rate_; // 最大旋转速率
    double focal_length_x_;    // x方向相机焦距
    double focal_length_y_;    // y方向相机焦距

    /**
     * @brief 限制速度在安全范围内
     * @param velocity 输入速度
     * @param limit 速度限制
     * @return 限制后的速度
     */
    double limitVelocity(double velocity, double limit);
};

} // namespace px4_tracker_control
