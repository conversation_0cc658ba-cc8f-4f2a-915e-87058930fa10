#ifndef PX4_CONTROL_HPP
#define PX4_CONTROL_HPP

#include <ros/ros.h>
#include <mavros_msgs/State.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <object_tracker/TrackerOffset.h>
#include <px4_tracker_control/TrackingState.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <px4_tracker_control/pid_controller.hpp>
#include <px4_tracker_control/ibvs_controller.hpp>

class PX4ControlNode {
private:
    ros::NodeHandle nh_;                    // ROS节点句柄，用于管理节点资源
    ros::Subscriber state_sub_;             // 订阅PX4飞行器状态信息的订阅者
    ros::Subscriber tracker_offset_sub_;    // 订阅目标跟踪算法输出的偏移量信息的订阅者
    ros::Subscriber local_pos_sub_;         // 订阅无人机位置信息的订阅者
    ros::Publisher local_pos_pub_;          // 发布位置控制指令的发布者
    ros::Publisher local_vel_pub_;          // 发布速度控制指令的发布者
    ros::ServiceClient arming_client_;      // 解锁/上锁飞行器的服务客户端
    ros::ServiceClient set_mode_client_;    // 设置飞行模式的服务客户端

    ros::Publisher tracking_state_pub_;    // 发布无人机跟踪状态信息的发布者
    ros::Subscriber tracking_state_sub_;   // 订阅无人机跟踪状态信息的订阅者

    // 话题名称参数
    std::string state_topic_;              // 飞行器状态话题名称
    std::string tracker_offset_topic_;     // 目标跟踪算法输出的偏移量话题名称
    std::string local_pos_topic_;          // 无人机位置控制话题名称
    std::string local_vel_topic_;          // 无人机速度控制话题名称
    std::string get_local_pos_topic_;      // 获取无人机位置话题名称
    std::string arming_service_;           // 解锁/上锁服务名称
    std::string set_mode_service_;         // 设置飞行模式服务名称
    std::string tracking_state_topic_;     // 无人机跟踪状态话题名称

    // 消息变量
    mavros_msgs::State current_state_;        // 当前PX4飞行器状态信息
    object_tracker::TrackerOffset tracker_offset_;  // 目标跟踪算法输出的偏移量信息
    geometry_msgs::TwistStamped vel_;        // 无人机速度控制指令
    geometry_msgs::PoseStamped pose_;        // 无人机位置控制指令
    px4_tracker_control::TrackingState tracking_state_; // tracker无人机跟踪状态信息

    // 坐标系相关
    std::string world_frame_; // 世界坐标系
    std::string uav_frame_; // 无人机坐标系
    tf2_ros::Buffer tf_buffer_; // 坐标变换缓存
    tf2_ros::TransformListener tf_listener_; // 坐标变换监听器

    // 跟踪超时检测状态变量
    bool tracking_started_;  // 是否开始跟踪状态变量
    double tracking_timeout_;  // 跟踪超时阈值
    ros::Time last_tracking_time_;  // 最后一次获得跟踪偏差量信息的时间

    // 时间变量
    ros::Time duration_time_start_;  // 计算时间间隔的开始时间



    // 无人机速度控制参数
    double max_velocity_;
    double velocity_scale_;

    // 起飞模式
    enum TAKEOFF_MODE {
        TAKEOFF_BY_VELOCITY,
        TAKEOFF_BY_POINTS,
    };
    TAKEOFF_MODE takeoff_mode_ = TAKEOFF_BY_POINTS; //起飞模式状态变量

    // 控制模式
    enum CONTROL_MODE {
        PID_CONTROL,
        IBVS_CONTROL,
        IBVS_SIMPLE_CONTROL
    };
    CONTROL_MODE control_mode_ = PID_CONTROL; //控制模式状态变量

    // 位置模式下的起飞位置
    double takeoff_position_x_= 0.0;
    double takeoff_position_y_= 0.0;
    double takeoff_position_z_= 0.0; //起飞高度

    // 速度模式下的起飞速度
    double takeoff_velocity_x_= 0.0;
    double takeoff_velocity_y_= 0.0;
    double takeoff_velocity_z_= 0.0; //起飞速度
    double takeoff_velocity_time_= 0.0; //起飞时间

    // 飞行器移动速度变量
    double move_velocity_x_= 0.0;
    double move_velocity_y_= 0.0;
    double move_velocity_z_= 0.0; //飞行器移动速度

    // 临时变量用于参数加载
    int takeoff_mode_int_ = 0;

    // PID控制器
    std::unique_ptr<px4_tracker_control::PIDController> x_pid_;
    std::unique_ptr<px4_tracker_control::PIDController> y_pid_;
    std::unique_ptr<px4_tracker_control::PIDController> z_pid_;

    // PID参数
    double kp_x_, ki_x_, kd_x_;
    double kp_y_, ki_y_, kd_y_;
    double kp_z_, ki_z_, kd_z_;

    // IBVS控制器
    std::unique_ptr<px4_tracker_control::IBVSController> ibvs_controller_;

    // IBVS参数
    double lambda_gain_;
    double velocity_limit_;
    double error_threshold_;
    double damping_factor_;
    double max_rotation_rate_;
    double focal_length_x_;
    double focal_length_y_;
    int control_mode_int_;

    /**
     * @brief 状态回调函数，用于更新当前PX4飞行器状态信息
     * @param msg 接收到的mavros_msgs::State类型消息
     */
    void stateCallback(const mavros_msgs::State::ConstPtr& msg);

    /**
     * @brief 目标跟踪偏移量回调函数
     * @param msg 接收到的目标跟踪偏移量消息
     *
     * 该函数用于处理目标跟踪算法输出的偏移量信息:
     * 1. 更新跟踪偏移量数据
     * 2. 设置跟踪状态为已开始
     * 3. 更新最后一次获得跟踪信息的时间戳
     */
    void trackerOffsetCallback(const object_tracker::TrackerOffset::ConstPtr& msg);

    /**
     * @brief 跟踪状态回调函数，用于更新无人机跟踪状态信息
     * @param msg 接收到的px4_tracker_control::TrackingState类型消息
     */
    void trackingStateCallback(const px4_tracker_control::TrackingState::ConstPtr& msg);

    /**
     * @brief 将世界坐标系下的位置转换到无人机坐标系下的位置
     * @param world_pose 世界坐标系下的位置
     * @param local_pose 无人机坐标系下的位置
     * @return 是否转换成功
     */
    std::tuple<double, double, double> transformWorldToLocal(double position_x_, double position_y_, double position_z_);

    /**
     * @brief 发布无人机跟踪状态信息
     * @param tracking_success 是否跟踪成功
     */
    void pubTrackingState(bool tracking_success);

    // 主动获取最新消息
    bool getLatestPose(geometry_msgs::PoseStamped& pose);

    /**
     * @brief 通过位置点控制飞行器起飞
     *
     * 该函数通过发送目标位置点来控制飞行器起飞:
     * 1. 设置目标起飞位置
     * 2. 持续监控当前位置
     * 3. 计算与目标位置的距离
     * 4. 当距离小于阈值或超时时结束
     */
    void takeoffByPoints();



    /**
     * @brief 通过速度控制飞行器起飞
     *
     * 该函数通过发送速度指令来控制飞行器起飞:
     * 1. 设置目标起飞速度(线速度和角速度)
     * 2. 持续发送速度指令
     * 3. 当达到指定时间后结束
     */
    void takeoffByVelocity();


    /**
     * @brief 处理飞行器模式和解锁
     *
     * 该函数负责:
     * 1. 检查并设置OFFBOARD模式
     * 2. 在OFFBOARD模式下解锁飞行器
     *
     * 工作流程:
     * 1. 如果当前不是OFFBOARD模式,则尝试切换到OFFBOARD模式
     * 2. 如果当前是OFFBOARD模式且未解锁,则尝试解锁飞行器
     */
    void handleModeAndArming();

    /**
     * @brief 检查跟踪超时状态
     *
     * 该函数负责:
     * 1. 检查是否开始跟踪
     * 2. 计算距离上次跟踪的时间间隔
     * 3. 如果超过超时阈值,则停止跟踪
     *
     * 工作流程:
     * 1. 如果已开始跟踪,计算距离上次跟踪的时间
     * 2. 如果时间超过设定的超时阈值,将跟踪状态设为false
     * 3. 输出警告信息表示跟踪超时
     */
    void checkTrackingTimeout();

    /**
     * @brief 更新速度控制命令
     *
     * 该函数负责:
     * 1. 根据跟踪偏移计算速度控制命令
     * 2. 限制最大速度在安全范围内
     * 3. 在没有跟踪信息时停止移动
     *
     * 工作流程:
     * 1. 如果正在跟踪目标:
     *    - 根据跟踪偏移计算x、y、z方向的速度
     *    - 限制最大速度不超过设定阈值
     * 2. 如果没有跟踪信息:
     *    - 将所有速度分量设为0
     *    - 停止所有运动
     */
    void updateVelocityCommand();

public:
    PX4ControlNode(); // 构造函数
    void run(); // 运行函数
};

#endif // PX4_CONTROL_HPP