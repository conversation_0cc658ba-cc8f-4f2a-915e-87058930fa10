#include "px4_tracker_control/pid_controller.hpp"
#include <cmath>

namespace px4_tracker_control {

PIDController::PIDController(double kp, double ki, double kd, double output_limit)
    : kp_(kp)
    , ki_(ki)
    , kd_(kd)
    , output_limit_(output_limit)
    , integral_(0.0)
    , last_error_(0.0)
{
}

double PIDController::compute(double error) {
    // 计算积分项
    integral_ += error;
    
    // 计算微分项
    double derivative = error - last_error_;
    
    // 计算PID输出
    double output = kp_ * error + ki_ * integral_ + kd_ * derivative;
    
    // 输出限幅
    if (output > output_limit_) {
        output = output_limit_;
    } else if (output < -output_limit_) {
        output = -output_limit_;
    }
    
    // 更新状态
    last_error_ = error;
    
    return output;
}

void PIDController::reset() {
    integral_ = 0.0;
    last_error_ = 0.0;
}

void PIDController::setParameters(double kp, double ki, double kd) {
    kp_ = kp;
    ki_ = ki;
    kd_ = kd;
}

} // namespace px4_tracker_control 