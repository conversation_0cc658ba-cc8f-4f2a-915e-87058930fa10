#include "px4_tracker_control/ibvs_controller.hpp"

namespace px4_tracker_control {

IBVSController::IBVSController(double lambda_gain, double velocity_limit, double error_threshold,
                             double damping_factor, double max_rotation_rate,
                             double focal_length_x, double focal_length_y)
    : lambda_gain_(lambda_gain)
    , velocity_limit_(velocity_limit)
    , error_threshold_(error_threshold)
    , damping_factor_(damping_factor)
    , max_rotation_rate_(max_rotation_rate)
    , focal_length_x_(focal_length_x)
    , focal_length_y_(focal_length_y)
{
}

std::tuple<double, double, double, double> IBVSController::compute(double delta_x, double delta_y, double depth) {
    // 计算误差的平方和
    double error_magnitude = std::sqrt(delta_x*delta_x + delta_y*delta_y);

    // 如果误差小于阈值，则停止控制
    if (error_magnitude < error_threshold_) {
        return std::make_tuple(0.0, 0.0, 0.0, 0.0);
    }

    // 将像素偏差转换为归一化坐标，使用不同的焦距值
    double x = delta_x / focal_length_x_;
    double y = delta_y / focal_length_y_;

    // 构建交互矩阵（图像雅可比矩阵）
    // 这里使用简化的交互矩阵，只控制无人机的x, y, z位置和yaw角
    Eigen::MatrixXd L(2, 6);
    L << -focal_length_x_/depth, 0, x, x*y/focal_length_x_, -(focal_length_x_*focal_length_x_ + x*x)/focal_length_x_, y,
         0, -focal_length_y_/depth, y, (focal_length_y_*focal_length_y_ + y*y)/focal_length_y_, -x*y/focal_length_y_, -x;

    // 构建误差向量
    Eigen::VectorXd error(2);
    error << x, y;

    // 使用Levenberg-Marquardt方法计算控制律
    Eigen::MatrixXd L_trans = L.transpose();
    Eigen::MatrixXd H = L_trans * L;
    Eigen::MatrixXd Hdiag = H.diagonal().asDiagonal();
    Eigen::MatrixXd H_sum = H + damping_factor_ * Hdiag;
    Eigen::MatrixXd H_sum_inverse = H_sum.completeOrthogonalDecomposition().pseudoInverse();

    // 计算相机速度
    Eigen::VectorXd camera_vel = -lambda_gain_ * H_sum_inverse * L_trans * error;

    // 提取速度分量
    double vx_cam = camera_vel(0);
    double vy_cam = camera_vel(1);
    double vz_cam = camera_vel(2);
    double wx_cam = camera_vel(3);
    double wy_cam = camera_vel(4);
    double wz_cam = camera_vel(5);

    // 将相机速度转换为无人机速度
    // 假设相机坐标系与无人机坐标系的关系：
    // 相机z轴对应无人机前进方向，相机x轴对应无人机右侧，相机y轴对应无人机下方
    // 修改为只在y轴方向运动
    double vx_drone = 0.0;    // 前后运动设为0
    double vy_drone = limitVelocity(-vx_cam, velocity_limit_);   // 左右运动
    double vz_drone = 0.0;   // 上下运动设为0
    double yaw_rate = 0.0; // 偏航角速度设为0

    return std::make_tuple(vx_drone, vy_drone, vz_drone, yaw_rate);
}

std::tuple<double, double, double, double> IBVSController::computeSimple(double delta_x, double delta_y, double depth) {
    // 简单的比例控制，使用不同的焦距值进行归一化
    // 修改为只在y轴方向运动
    // 前后运动设为0
    double vx_drone = 0.0;

    // 左右运动与x方向偏差成正比
    double vy_drone = -lambda_gain_ * delta_x / focal_length_x_;

    // 上下运动保持为0
    double vz_drone = 0.0;

    // 偏航角速度设为0
    double yaw_rate = 0.0;

    // 限制速度
    vx_drone = limitVelocity(vx_drone, velocity_limit_);
    vy_drone = limitVelocity(vy_drone, velocity_limit_);
    vz_drone = limitVelocity(vz_drone, velocity_limit_);
    yaw_rate = limitVelocity(yaw_rate, max_rotation_rate_);

    return std::make_tuple(vx_drone, vy_drone, vz_drone, yaw_rate);
}

void IBVSController::setParameters(double lambda_gain, double velocity_limit, double error_threshold,
                                 double damping_factor, double max_rotation_rate) {
    lambda_gain_ = lambda_gain;
    velocity_limit_ = velocity_limit;
    error_threshold_ = error_threshold;
    damping_factor_ = damping_factor;
    max_rotation_rate_ = max_rotation_rate;
}

double IBVSController::limitVelocity(double velocity, double limit) {
    return std::max(std::min(velocity, limit), -limit);
}

} // namespace px4_tracker_control
