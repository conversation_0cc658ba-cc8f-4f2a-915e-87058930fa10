#include <ros/ros.h>
#include <std_msgs/String.h>
#include <mavros_msgs/CommandBool.h>
#include <mavros_msgs/SetMode.h>
#include <mavros_msgs/State.h>
#include <mavros_msgs/PositionTarget.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <object_tracker/TrackerOffset.h>
#include <geometry_msgs/Twist.h>

class PX4TrackerControlNode {
private:
    ros::NodeHandle nh_;                    // ROS节点句柄，用于管理节点资源
    ros::Subscriber state_sub_;             // 订阅PX4飞行器状态信息的订阅者
    ros::Subscriber tracker_offset_sub_;    // 订阅目标跟踪算法输出的偏移量信息的订阅者
    ros::Subscriber local_pos_sub_;         // 订阅无人机位置信息的订阅者
    ros::Publisher local_pos_pub_;          // 发布位置控制指令的发布者
    ros::Publisher local_vel_pub_;          // 发布速度控制指令的发布者
    ros::ServiceClient arming_client_;      // 解锁/上锁飞行器的服务客户端
    ros::ServiceClient set_mode_client_;    // 设置飞行模式的服务客户端
    
    // 话题名称参数
    std::string state_topic_;              // 飞行器状态话题名称
    std::string tracker_offset_topic_;     // 目标跟踪算法输出的偏移量话题名称
    std::string local_pos_topic_;          // 无人机位置控制话题名称
    std::string local_vel_topic_;          // 无人机速度控制话题名称
    std::string get_local_pos_topic_;      // 获取无人机位置话题名称
    std::string arming_service_;           // 解锁/上锁服务名称
    std::string set_mode_service_;         // 设置飞行模式服务名称
    
    // 状态变量
    mavros_msgs::State current_state_;        // 当前PX4飞行器状态信息
    object_tracker::TrackerOffset tracker_offset_;  // 目标跟踪算法输出的偏移量信息
    geometry_msgs::TwistStamped vel_;        // 无人机速度控制指令
    geometry_msgs::PoseStamped pose_;        // 无人机位置控制指令

    // 跟踪超时检测状态变量
    bool tracking_started_;  // 是否开始跟踪状态变量
    double tracking_timeout_;  // 跟踪超时阈值
    ros::Time last_tracking_time_;  // 最后一次获得跟踪偏差量信息的时间

    // 无人机速度控制参数
    double max_velocity_;
    double velocity_scale_;

    // 起飞模式
    enum TAKEOFF_MODE {
        TAKEOFF_BY_VELOCITY,
        TAKEOFF_BY_POINTS,        
    };
    TAKEOFF_MODE takeoff_mode_ = TAKEOFF_BY_POINTS; //起飞模式状态变量

    // 位置模式下的起飞位置
    double takeoff_position_x_= 0.0; 
    double takeoff_position_y_= 0.0;
    double takeoff_position_z_= 0.0; //起飞高度

    // 速度模式下的起飞速度
    double takeoff_velocity_x_= 0.0;
    double takeoff_velocity_y_= 0.0;
    double takeoff_velocity_z_= 0.0; //起飞速度
    double takeoff_velocity_time_= 0.0; //起飞时间

    // 临时变量用于参数加载
    int takeoff_mode_int_ = 0;

    /**
     * @brief 状态回调函数，用于更新当前PX4飞行器状态信息
     * @param msg 接收到的mavros_msgs::State类型消息
     */
    void stateCallback(const mavros_msgs::State::ConstPtr& msg) {
        current_state_ = *msg;  // 更新当前状态信息
    }
    
    /**
     * @brief 目标跟踪偏移量回调函数
     * @param msg 接收到的目标跟踪偏移量消息
     * 
     * 该函数用于处理目标跟踪算法输出的偏移量信息:
     * 1. 更新跟踪偏移量数据
     * 2. 设置跟踪状态为已开始
     * 3. 更新最后一次获得跟踪信息的时间戳
     */
    void trackerOffsetCallback(const object_tracker::TrackerOffset::ConstPtr& msg) {
        tracker_offset_ = *msg;
        tracking_started_ = true;
        last_tracking_time_ = ros::Time::now();
    }
    
    // 主动获取最新消息
    bool getLatestPose(geometry_msgs::PoseStamped& pose) {
        // 使用ros::topic::waitForMessage等待最新消息
        boost::shared_ptr<geometry_msgs::PoseStamped const> shared_pose;
        shared_pose = ros::topic::waitForMessage<geometry_msgs::PoseStamped>(
            local_pos_topic_, ros::Duration(0.1));
        
        if (shared_pose != NULL) {
            pose = *shared_pose;
            return true;
        }
        return false;
    }    
    
    /**
     * @brief 通过位置点控制飞行器起飞
     * 
     * 该函数通过发送目标位置点来控制飞行器起飞:
     * 1. 设置目标起飞位置
     * 2. 持续监控当前位置
     * 3. 计算与目标位置的距离
     * 4. 当距离小于阈值或超时时结束
     */
    void takeoffByPoints() {
        // 定义位置控制相关参数
        const double POSITION_THRESHOLD = 0.1;   // 位置阈值,单位米
        const double TIMEOUT = 10.0;             // 超时时间,单位秒
        geometry_msgs::PoseStamped current_pose;  // 当前位置信息
        double distance = 999;                   // 当前位置和目标点距离
        double dx, dy, dz = 0;                   // 当前位置和目标点位置的差值
        ros::Time start_wait_time = ros::Time::now(); // 开始等待时间

        // 设置目标起飞位置
        pose_.header.stamp = ros::Time::now();
        pose_.pose.position.x = takeoff_position_x_;  // 目标X坐标
        pose_.pose.position.y = takeoff_position_y_;  // 目标Y坐标
        pose_.pose.position.z = takeoff_position_z_;  // 目标Z坐标(起飞高度)
            
        // 循环控制直到达到目标位置或超时
        while(ros::ok() && distance > POSITION_THRESHOLD) {
            getLatestPose(current_pose);  // 获取最新位置信息
            
            // 计算当前位置与目标位置的欧氏距离
            dx = current_pose.pose.position.x - takeoff_position_x_;
            dy = current_pose.pose.position.y - takeoff_position_y_;
            dz = current_pose.pose.position.z - takeoff_position_z_;
            distance = sqrt(dx*dx + dy*dy + dz*dz);

            local_pos_pub_.publish(pose_);  // 发布目标位置
            ros::Duration(0.1).sleep();     // 控制频率10Hz

            // 检查是否超时
            if((ros::Time::now() - start_wait_time).toSec() > TIMEOUT) {
                ROS_INFO("takeoff position TIMEOUT");
                return;
            }
        }
        ROS_INFO("takeoff position reached");
    }
    


    /**
     * @brief 通过速度控制飞行器起飞
     * 
     * 该函数通过发送速度指令来控制飞行器起飞:
     * 1. 设置目标起飞速度(线速度和角速度)
     * 2. 持续发送速度指令
     * 3. 当达到指定时间后结束
     */
    void takeoffByVelocity() {
        // 设置速度指令的时间戳
        vel_.header.stamp = ros::Time::now();
        
        // 设置线速度分量
        vel_.twist.linear.x = takeoff_velocity_x_;  // X方向速度
        vel_.twist.linear.y = takeoff_velocity_y_;  // Y方向速度
        vel_.twist.linear.z = takeoff_velocity_z_;  // Z方向速度(上升速度)
        
        // 设置角速度分量为0,保持姿态稳定
        vel_.twist.angular.x = 0.0;  // 横滚角速度
        vel_.twist.angular.y = 0.0;  // 俯仰角速度
        vel_.twist.angular.z = 0.0;  // 偏航角速度
        
        // 记录开始时间
        ros::Time start_time = ros::Time::now();
        
        // 在指定时间内持续发送速度指令
        while(ros::ok() && (ros::Time::now() - start_time).toSec() < takeoff_velocity_time_) {
            local_vel_pub_.publish(vel_);  // 发布速度指令
            ros::Duration(0.1).sleep();    // 控制频率10Hz
        }
        
        // 输出完成信息
        ROS_INFO("takeoffByVelocity finished");
    }
    
    /**
     * @brief 处理飞行器模式和解锁
     * 
     * 该函数负责:
     * 1. 检查并设置OFFBOARD模式
     * 2. 在OFFBOARD模式下解锁飞行器
     * 
     * 工作流程:
     * 1. 如果当前不是OFFBOARD模式,则尝试切换到OFFBOARD模式
     * 2. 如果当前是OFFBOARD模式且未解锁,则尝试解锁飞行器
     */
    void handleModeAndArming() {

        // 在设置模式和解锁前发送一些位置点，防止起飞时位置偏移，*******必须发送**********
        geometry_msgs::PoseStamped pose;
        pose.pose.position.x = 0;
        pose.pose.position.y = 0;
        pose.pose.position.z = 0;
    
        //send a few setpoints before starting
        for(int i = 100; ros::ok() && i > 0; --i){
            local_pos_pub_.publish(pose);
            ros::Duration(0.1).sleep();
        }

 
        // 开始设置模式、解锁
        ros::Time start_time = ros::Time::now(); // 记录开始时间

        // 设置OFFBOARD模式
        mavros_msgs::SetMode offb_set_mode;
        offb_set_mode.request.custom_mode = "OFFBOARD";
        while(ros::ok() && current_state_.mode != "OFFBOARD") {            
            // 调用服务切换模式并检查响应
            if(set_mode_client_.call(offb_set_mode) && offb_set_mode.response.mode_sent) {
                ROS_INFO("Offboard mode enabled");
                start_time = ros::Time::now(); // 更新开始时间
            }

            local_pos_pub_.publish(pose);  // 发布位置点，维持发送心跳信号
            ros::spinOnce();   // 处理回调函数
            ros::Duration(0.1).sleep(); // 控制频率10Hz

            // 检查是否超时
            if((ros::Time::now() - start_time).toSec() > 10.0) {
                ROS_ERROR("Failed to switch to OFFBOARD mode");
                return;
            }
        }
        
        // 解锁
        mavros_msgs::CommandBool arm_cmd;
        arm_cmd.request.value = true;
        while(ros::ok() && !current_state_.armed ) {
            // 设置offboard模式超过5秒，则解锁
            if (ros::Time::now() - start_time > ros::Duration(5.0)) {               
                // 调用服务解锁并检查响应
                if(arming_client_.call(arm_cmd) && arm_cmd.response.success) {
                    ROS_INFO("Vehicle armed");
                }
            }

            local_pos_pub_.publish(pose);  // 发布位置点，维持发送心跳信号
            ros::spinOnce();   // 处理回调函数
            ros::Duration(0.1).sleep(); // 控制频率10Hz

            // 检查是否超时
            if((ros::Time::now() - start_time).toSec() > 10.0) {
                ROS_ERROR("Failed to arm vehicle");
                return;
            }
        }
    }
    
    /**
     * @brief 检查跟踪超时状态
     * 
     * 该函数负责:
     * 1. 检查是否开始跟踪
     * 2. 计算距离上次跟踪的时间间隔
     * 3. 如果超过超时阈值,则停止跟踪
     * 
     * 工作流程:
     * 1. 如果已开始跟踪,计算距离上次跟踪的时间
     * 2. 如果时间超过设定的超时阈值,将跟踪状态设为false
     * 3. 输出警告信息表示跟踪超时
     */
    void checkTrackingTimeout() {
        if(tracking_started_) {
            ros::Duration time_since_last_tracking = ros::Time::now() - last_tracking_time_;
            if(time_since_last_tracking.toSec() > tracking_timeout_) {
                tracking_started_ = false;
                ROS_WARN("Tracking timeout - stopping movement");
            }
        }
    }
    
    /**
     * @brief 更新速度控制命令
     * 
     * 该函数负责:
     * 1. 根据跟踪偏移计算速度控制命令
     * 2. 限制最大速度在安全范围内
     * 3. 在没有跟踪信息时停止移动
     * 
     * 工作流程:
     * 1. 如果正在跟踪目标:
     *    - 根据跟踪偏移计算x、y、z方向的速度
     *    - 限制最大速度不超过设定阈值
     * 2. 如果没有跟踪信息:
     *    - 将所有速度分量设为0
     *    - 停止所有运动
     */
    void updateVelocityCommand() {
        if(tracking_started_) {
            // 根据跟踪偏移计算速度
            vel_.twist.linear.x = 0;  // 暂时不控制前進
            vel_.twist.linear.y = -tracker_offset_.offset_x * velocity_scale_;
            vel_.twist.linear.z = -tracker_offset_.offset_y * velocity_scale_;  
            vel_.twist.angular.z = 0;  // 暂时不控制偏航
            
            
            // 限制最大速度
            double speed = std::sqrt(vel_.twist.linear.x * vel_.twist.linear.x + 
                                   vel_.twist.linear.y * vel_.twist.linear.y + 
                                   vel_.twist.linear.z * vel_.twist.linear.z);
            if(speed > max_velocity_) {
                double scale = max_velocity_ / speed;
                vel_.twist.linear.x *= scale;
                vel_.twist.linear.y *= scale;
                vel_.twist.linear.z *= scale;
            }
            ROS_INFO("vel.twist.linear.y: %f , vel.twist.linear.z: %f", vel_.twist.linear.y, vel_.twist.linear.z);     
        } else {
            // 如果没有跟踪信息，停止移动
            vel_.twist.linear.x = 0;
            vel_.twist.linear.y = 0;
            vel_.twist.linear.z = 0;
            vel_.twist.angular.x = 0.0;
            vel_.twist.angular.y = 0.0;
            vel_.twist.angular.z = 0.0;
        }
        
    }

public:
    PX4TrackerControlNode() : nh_("~") {
        // 从参数服务器加载参数
        nh_.param<double>("max_velocity", max_velocity_, 1.0);
        nh_.param<double>("velocity_scale", velocity_scale_, 0.5);
        nh_.param<double>("tracking_timeout", tracking_timeout_, 1.0);
        
        // 加载话题名称参数
        nh_.param<std::string>("state_topic", state_topic_, "mavros/state");
        nh_.param<std::string>("tracker_offset_topic", tracker_offset_topic_, "/tracker_offset");
        nh_.param<std::string>("local_pos_topic", local_pos_topic_, "mavros/setpoint_position/local");
        nh_.param<std::string>("local_vel_topic", local_vel_topic_, "mavros/setpoint_velocity/cmd_vel");
        nh_.param<std::string>("get_local_pos_topic", get_local_pos_topic_, "mavros/local_position/pose");
        nh_.param<std::string>("arming_service", arming_service_, "mavros/cmd/arming");
        nh_.param<std::string>("set_mode_service", set_mode_service_, "mavros/set_mode");

        // 加载起飞模式参数
        nh_.param<int>("takeoff_mode", takeoff_mode_int_, 1);   // 1表示TAKEOFF_BY_POINTS
        takeoff_mode_ = static_cast<TAKEOFF_MODE>(takeoff_mode_int_);
        
        nh_.param<double>("takeoff_position_x", takeoff_position_x_, 0.0);
        nh_.param<double>("takeoff_position_y", takeoff_position_y_, 0.0);
        nh_.param<double>("takeoff_position_z", takeoff_position_z_, 2.0);
        nh_.param<double>("takeoff_velocity_x", takeoff_velocity_x_, 0.0);
        nh_.param<double>("takeoff_velocity_y", takeoff_velocity_y_, 0.0);
        nh_.param<double>("takeoff_velocity_z", takeoff_velocity_z_, 1.0);
        nh_.param<double>("takeoff_velocity_time", takeoff_velocity_time_ , 5.0);

        // 初始化发布者和订阅者，客戶端
        state_sub_ = nh_.subscribe<mavros_msgs::State>(state_topic_, 10, &PX4TrackerControlNode::stateCallback, this);
        tracker_offset_sub_ = nh_.subscribe<object_tracker::TrackerOffset>(tracker_offset_topic_, 10, &PX4TrackerControlNode::trackerOffsetCallback, this);

        local_pos_pub_ = nh_.advertise<geometry_msgs::PoseStamped>(local_pos_topic_, 10);
        local_vel_pub_ = nh_.advertise<geometry_msgs::TwistStamped>(local_vel_topic_, 10);

        arming_client_ = nh_.serviceClient<mavros_msgs::CommandBool>(arming_service_);
        set_mode_client_ = nh_.serviceClient<mavros_msgs::SetMode>(set_mode_service_);

        // 等待FCU连接
        while(ros::ok() && !current_state_.connected) {
            ros::spinOnce();
            ros::Duration(0.1).sleep();
        }
        ROS_INFO("FCU connected");

        // 尚未进入跟踪状态
        tracking_started_ = false;

        // 设置OFFBOARD模式，并解锁
        handleModeAndArming();

        // 根据起飞模式选择起飞方式
        if(takeoff_mode_ == TAKEOFF_BY_VELOCITY) {
            // 以速度方式起飞
            takeoffByVelocity();
        } else if(takeoff_mode_ == TAKEOFF_BY_POINTS) {
            // 以位置方式起飞
            takeoffByPoints();
        }

    }
    
    void run() {    
        ros::Rate rate(40); // 40Hz

        // 主循环
        while(ros::ok()) {
            checkTrackingTimeout();  // 检查跟踪超时
            updateVelocityCommand(); // 更新速度指令
            local_vel_pub_.publish(vel_); // 发布速度指令
            ros::spinOnce(); // 处理回调函数
            rate.sleep(); // 等待下一个循环
        }
    }
};

int main(int argc, char **argv) {
    ros::init(argc, argv, "px4_tracker_control_node");
    PX4TrackerControlNode node;
    node.run();
    return 0;
} 