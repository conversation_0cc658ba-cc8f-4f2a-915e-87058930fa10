<?xml version="1.0"?>
<launch>
    
    <!-- 坐标系参数，mavros坐标系比较特殊，暂时没搞懂 -->
    <!-- <arg name="world_frame" default="map_ned"/>
    <arg name="uav_frame" default="base_link_frd"/> -->
    
    <!-- 加载参数 -->
    <arg name="state_topic" default="/uav0/mavros/state"/>
    <arg name="tracker_offset_topic" default="/tracker_offset"/>
    <arg name="local_pos_topic" default="/uav0/mavros/setpoint_position/local"/>
    <arg name="local_vel_topic" default="/uav0/mavros/setpoint_velocity/cmd_vel"/>
    <arg name="get_local_pos_topic" default="/uav0/mavros/local_position/pose"/>
    <arg name="arming_service" default="/uav0/mavros/cmd/arming"/>
    <arg name="set_mode_service" default="/uav0/mavros/set_mode"/>
    
    <!-- 控制参数 -->
    <arg name="max_velocity" default="50"/>
    <arg name="velocity_scale" default="1"/>
    <arg name="tracking_timeout" default="1.0"/>
    
    <!-- TAKEOFF_BY_POINTS位置起飞参数，为地图坐标系坐标-->
    <arg name="takeoff_mode" default="1"/>
    <arg name="takeoff_position_x" default="0.0"/>
    <arg name="takeoff_position_y" default="0.0"/>
    <arg name="takeoff_position_z" default="10.0"/>

    <!-- TAKEOFF_BY_VELOCITY速度起飞参数 用这个-->
    <!-- <arg name="takeoff_mode" default="0"/>
    <arg name="takeoff_velocity_x" default="0.0"/>
    <arg name="takeoff_velocity_y" default="0.0"/>
    <arg name="takeoff_velocity_z" default="1.0"/>
    <arg name="takeoff_velocity_time" default="5.0"/> -->
    
    <!-- 在控制参数部分添加PID参数 -->
    <arg name="kp_x" default="0.1"/>
    <arg name="ki_x" default="0"/>
    <arg name="kd_x" default="0"/>
    <!-- <arg name="kp_y" default="0.02"/> 增大这个值可以提高响应速度 0.04是持续震荡的阈值，震荡周期为3s-->
    <!-- <arg name="ki_y" default="0.00005"/> 增大这个值可以减少稳态误差 -->
    <!-- <arg name="kd_y" default="0.0000125"/> 增大这个值可以抑制超调 -->
    <!-- <arg name="kp_y" default="0.02"/> 增大这个值可以提高响应速度 0.04是持续震荡的阈值，震荡周期为3s-->
    <!-- <arg name="ki_y" default="0.01"/> 增大这个值可以减少稳态误差 -->
    <!-- <arg name="kd_y" default="0.003"/> 增大这个值可以抑制超调 -->
    <arg name="kp_y" default="10"/> <!-- 增大这个值可以提高响应速度 0.04是持续震荡的阈值，震荡周期为3s-->
    <arg name="ki_y" default="0"/> <!-- 增大这个值可以减少稳态误差 -->
    <arg name="kd_y" default="0"/> <!-- 增大这个值可以抑制超调 -->
    <!-- 高度控制参数 -->
    <arg name="kp_z" default="0"/>
    <arg name="ki_z" default="0"/>
    <arg name="kd_z" default="0"/>
    
    
    
    <!-- 启动节点 -->
    <node name="px4_control_node_tracker" pkg="px4_tracker_control" type="px4_control_node_tracker" output="screen">
        <!-- 坐标系参数 -->
        <!-- <param name="world_frame" value="$(arg world_frame)"/>
        <param name="uav_frame" value="$(arg uav_frame)"/> -->
        
        <remap from="tracking_state" to="/uav0/tracking_state"/>
        <!-- 话题名称参数 -->
        <param name="state_topic" value="$(arg state_topic)"/>
        <param name="tracker_offset_topic" value="$(arg tracker_offset_topic)"/>
        <param name="local_pos_topic" value="$(arg local_pos_topic)"/>
        <param name="local_vel_topic" value="$(arg local_vel_topic)"/>
        <param name="get_local_pos_topic" value="$(arg get_local_pos_topic)"/>
        <param name="arming_service" value="$(arg arming_service)"/>
        <param name="set_mode_service" value="$(arg set_mode_service)"/>
        
        <!-- 控制参数 -->
        <param name="max_velocity" value="$(arg max_velocity)"/>
        <param name="velocity_scale" value="$(arg velocity_scale)"/>
        <param name="tracking_timeout" value="$(arg tracking_timeout)"/>
        
        <!-- 起飞参数 -->
        <param name="takeoff_mode" value="$(arg takeoff_mode)"/>
        <param name="takeoff_position_x" value="$(arg takeoff_position_x)"/>
        <param name="takeoff_position_y" value="$(arg takeoff_position_y)"/>
        <param name="takeoff_position_z" value="$(arg takeoff_position_z)"/>
        <!-- <param name="takeoff_velocity_x" value="$(arg takeoff_velocity_x)"/>
        <param name="takeoff_velocity_y" value="$(arg takeoff_velocity_y)"/>
        <param name="takeoff_velocity_z" value="$(arg takeoff_velocity_z)"/>
        <param name="takeoff_velocity_time" value="$(arg takeoff_velocity_time)"/> -->
        
        <!-- 在node标签中添加PID参数 -->
        <param name="kp_x" value="$(arg kp_x)"/>
        <param name="ki_x" value="$(arg ki_x)"/>
        <param name="kd_x" value="$(arg kd_x)"/>
        <param name="kp_y" value="$(arg kp_y)"/>
        <param name="ki_y" value="$(arg ki_y)"/>
        <param name="kd_y" value="$(arg kd_y)"/>
        <param name="kp_z" value="$(arg kp_z)"/>
        <param name="ki_z" value="$(arg ki_z)"/>
        <param name="kd_z" value="$(arg kd_z)"/>
    </node>
</launch> 