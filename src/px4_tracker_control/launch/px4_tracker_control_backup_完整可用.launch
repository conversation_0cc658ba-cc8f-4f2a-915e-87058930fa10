<?xml version="1.0"?>
<launch>
    
    <!-- 加载参数 -->
    <arg name="state_topic" default="/uav0/mavros/state"/>
    <arg name="tracker_offset_topic" default="/tracker_offset"/>
    <arg name="local_pos_topic" default="/uav0/mavros/setpoint_position/local"/>
    <arg name="local_vel_topic" default="/uav0/mavros/setpoint_velocity/cmd_vel"/>
    <arg name="get_local_pos_topic" default="/uav0/mavros/local_position/pose"/>
    <arg name="arming_service" default="/uav0/mavros/cmd/arming"/>
    <arg name="set_mode_service" default="/uav0/mavros/set_mode"/>
    
    <!-- 控制参数 -->
    <arg name="max_velocity" default="1.0"/>
    <arg name="velocity_scale" default="0.5"/>
    <arg name="tracking_timeout" default="1.0"/>
    
    <!-- TAKEOFF_BY_POINTS位置起飞参数 用这个-->
    <arg name="takeoff_mode" default="1"/>
    <arg name="takeoff_position_x" default="0.0"/>
    <arg name="takeoff_position_y" default="0.0"/>
    <arg name="takeoff_position_z" default="10.0"/>

    <!-- TAKEOFF_BY_VELOCITY速度起飞参数 用这个-->
    <!-- <arg name="takeoff_mode" default="0"/>
    <arg name="takeoff_velocity_x" default="0.0"/>
    <arg name="takeoff_velocity_y" default="0.0"/>
    <arg name="takeoff_velocity_z" default="1.0"/>
    <arg name="takeoff_velocity_time" default="5.0"/> -->
    
    <!-- 启动px4仿真节点 -->
    <include file="$(find px4)/launch/multi_uav_mavros_sitl_sdf_my.launch"/>
    
    <!-- 启动节点 -->
    <node name="px4_tracker_control_node" pkg="px4_tracker_control" type="px4_tracker_control_node" output="screen">
        
        <!-- 话题名称参数 -->
        <param name="state_topic" value="$(arg state_topic)"/>
        <param name="tracker_offset_topic" value="$(arg tracker_offset_topic)"/>
        <param name="local_pos_topic" value="$(arg local_pos_topic)"/>
        <param name="local_vel_topic" value="$(arg local_vel_topic)"/>
        <param name="get_local_pos_topic" value="$(arg get_local_pos_topic)"/>
        <param name="arming_service" value="$(arg arming_service)"/>
        <param name="set_mode_service" value="$(arg set_mode_service)"/>
        
        <!-- 控制参数 -->
        <param name="max_velocity" value="$(arg max_velocity)"/>
        <param name="velocity_scale" value="$(arg velocity_scale)"/>
        <param name="tracking_timeout" value="$(arg tracking_timeout)"/>
        
        <!-- 起飞参数 -->
        <param name="takeoff_mode" value="$(arg takeoff_mode)"/>
        <param name="takeoff_position_x" value="$(arg takeoff_position_x)"/>
        <param name="takeoff_position_y" value="$(arg takeoff_position_y)"/>
        <param name="takeoff_position_z" value="$(arg takeoff_position_z)"/>
        <!-- <param name="takeoff_velocity_x" value="$(arg takeoff_velocity_x)"/>
        <param name="takeoff_velocity_y" value="$(arg takeoff_velocity_y)"/>
        <param name="takeoff_velocity_z" value="$(arg takeoff_velocity_z)"/>
        <param name="takeoff_velocity_time" value="$(arg takeoff_velocity_time)"/> -->
    </node>
</launch> 