<?xml version="1.0"?>
<launch>
    
    <!-- 坐标系参数，mavros坐标系比较特殊，暂时没搞懂 -->
    <!-- <arg name="world_frame" default="map_ned"/>
    <arg name="uav_frame" default="uav1/local_origin"/> -->
    
    <!-- 加载参数 -->
    <arg name="state_topic" default="/uav1/mavros/state"/>
    <arg name="local_pos_topic" default="/uav1/mavros/setpoint_position/local"/>
    <arg name="local_vel_topic" default="/uav1/mavros/setpoint_velocity/cmd_vel"/>
    <arg name="get_local_pos_topic" default="/uav1/mavros/local_position/pose"/>
    <arg name="arming_service" default="/uav1/mavros/cmd/arming"/>
    <arg name="set_mode_service" default="/uav1/mavros/set_mode"/>
    <arg name="tracking_state_topic" default="/px4_control_node_tracker/tracking_state"/>
    
    <!-- 飞行器最大移动速度参数，暂时不起作用 -->
    <arg name="max_velocity" default="1.0"/>
    
    <!-- TAKEOFF_BY_POINTS位置起飞参数 用这个-->
    <arg name="takeoff_mode" default="1"/>
    <arg name="takeoff_position_x" default="0.0"/>
    <arg name="takeoff_position_y" default="0.0"/>
    <arg name="takeoff_position_z" default="10.0"/>

    <!-- 飞行器移动速度参数 -->
    <arg name="move_velocity_x" default="-20.0"/>
    <arg name="move_velocity_y" default="0"/>
    <arg name="move_velocity_z" default="0.0"/>

    <!-- TAKEOFF_BY_VELOCITY速度起飞参数 用这个-->
    <!-- <arg name="takeoff_mode" default="0"/>
    <arg name="takeoff_velocity_x" default="0.0"/>
    <arg name="takeoff_velocity_y" default="0.0"/>
    <arg name="takeoff_velocity_z" default="1.0"/>
    <arg name="takeoff_velocity_time" default="5.0"/> -->
   
    
    <!-- 启动节点 -->
    <node name="px4_control_node_target" pkg="px4_tracker_control" type="px4_control_node_target" output="screen">
        
        <!-- 坐标系参数 -->
        <!-- <param name="world_frame" value="$(arg world_frame)"/>
        <param name="uav_frame" value="$(arg uav_frame)"/> -->
        
        <!-- 话题名称参数 -->
        <param name="state_topic" value="$(arg state_topic)"/>
        <param name="local_pos_topic" value="$(arg local_pos_topic)"/>
        <param name="local_vel_topic" value="$(arg local_vel_topic)"/>
        <param name="get_local_pos_topic" value="$(arg get_local_pos_topic)"/>
        <param name="arming_service" value="$(arg arming_service)"/>
        <param name="set_mode_service" value="$(arg set_mode_service)"/>
        <param name="tracking_state_topic" value="$(arg tracking_state_topic)"/>        
        <!-- 控制参数 -->
        <param name="max_velocity" value="$(arg max_velocity)"/>
        
        <!-- 起飞参数 -->
        <param name="takeoff_mode" value="$(arg takeoff_mode)"/>
        <param name="takeoff_position_x" value="$(arg takeoff_position_x)"/>
        <param name="takeoff_position_y" value="$(arg takeoff_position_y)"/>
        <param name="takeoff_position_z" value="$(arg takeoff_position_z)"/>
        <!-- <param name="takeoff_velocity_x" value="$(arg takeoff_velocity_x)"/>
        <param name="takeoff_velocity_y" value="$(arg takeoff_velocity_y)"/>
        <param name="takeoff_velocity_z" value="$(arg takeoff_velocity_z)"/>
        <param name="takeoff_velocity_time" value="$(arg takeoff_velocity_time)"/> -->

        <!-- 飞行器移动速度参数 -->
        <param name="move_velocity_x" value="$(arg move_velocity_x)"/>
        <param name="move_velocity_y" value="$(arg move_velocity_y)"/>
        <param name="move_velocity_z" value="$(arg move_velocity_z)"/>
    </node>
</launch> 