#include "takeoff_func.hpp"
#include <ros/ros.h>
#include <mavros_msgs/CommandBool.h>
#include <mavros_msgs/SetMode.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <sensor_msgs/Image.h>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>

TakeoffFunc::TakeoffFunc() : 
    set_mode_client_(nh_.serviceClient<mavros_msgs::SetMode>("/uav0/mavros/set_mode")),
    arming_client_(nh_.serviceClient<mavros_msgs::CommandBool>("/uav0/mavros/cmd/arming")),
    target_offset_x_(0.0), target_offset_y_(0.0), rate_(20.0)
{
    // 初始化发布器和订阅器
    vel_pub_ = nh_.advertise<geometry_msgs::TwistStamped>("/uav0/mavros/setpoint_velocity/cmd_vel", 10);
    state_sub_ = nh_.subscribe<mavros_msgs::State>("/uav0/mavros/state", 10, &TakeoffFunc::stateCallback, this);

    // wait for FCU connection
    while(ros::ok() && !current_state_.connected){
        ros::spinOnce();
        rate_.sleep();
    }   
}

void TakeoffFunc::stateCallback(const mavros_msgs::State::ConstPtr& msg) {
    current_state_ = *msg;
}

bool TakeoffFunc::enterOffboardAndArm() {
    mavros_msgs::SetMode offboard_mode;
    offboard_mode.request.custom_mode = "OFFBOARD";

    mavros_msgs::CommandBool arm_cmd;
    arm_cmd.request.value = true;

    ros::Time last_request = ros::Time::now();
    
    while(current_state_.mode != "OFFBOARD" && !current_state_.armed){
        if( current_state_.mode != "OFFBOARD" &&
            (ros::Time::now() - last_request > ros::Duration(5.0))){
            if( set_mode_client_.call(offboard_mode) &&
                offboard_mode.response.mode_sent){
                ROS_INFO("Offboard enabled");
            }
            last_request = ros::Time::now();
        } else {
            if( current_state_.mode == "OFFBOARD" && !current_state_.armed &&
                (ros::Time::now() - last_request > ros::Duration(5.0))){
                if( arming_client_.call(arm_cmd) &&
                    arm_cmd.response.success){
                    ROS_INFO("Vehicle armed");
                }
                last_request = ros::Time::now();
            }
        }
    }
    return true;
}

bool TakeoffFunc::takeoff(float speed, float time) {
    geometry_msgs::TwistStamped vel_msg;
    
    // 持续发送速度指令保持悬停
    for (int i = 0; i < 100; ++i) {
        vel_pub_.publish(vel_msg);
        rate_.sleep();
        ros::spinOnce();
    }
    
    // 垂直上升
    vel_msg.twist.linear.z = speed;
    ros::Time start_time = ros::Time::now();
    while (ros::ok() && (ros::Time::now() - start_time) < ros::Duration(time)) {
        vel_pub_.publish(vel_msg);
        rate_.sleep();
        ros::spinOnce();
    }
    return true;


    
}
