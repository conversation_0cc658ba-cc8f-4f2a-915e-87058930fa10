#ifndef VELOCITY_GENERATOR_HPP
#define VELOCITY_GENERATOR_HPP

#include <geometry_msgs/TwistStamped.h>
#include <object_tracker/TrackerOffset.h>

namespace px4_tracker_control {

class VelocityGenerator {
public:
    virtual ~VelocityGenerator() = default;
    
    // 根据跟踪信息生成速度命令
    virtual geometry_msgs::TwistStamped generateVelocity(
        const object_tracker::TrackerOffset& tracker_offset,
        bool tracking_started) = 0;
    
    // 设置最大速度限制
    virtual void setMaxVelocity(double max_velocity) = 0;
    
    // 设置速度缩放因子
    virtual void setVelocityScale(double scale) = 0;
};

} // namespace px4_tracker_control

#endif // VELOCITY_GENERATOR_HPP 