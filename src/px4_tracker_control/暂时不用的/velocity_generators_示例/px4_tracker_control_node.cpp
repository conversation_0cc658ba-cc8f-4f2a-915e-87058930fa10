#include <ros/ros.h>
#include <mavros_msgs/CommandBool.h>
#include <mavros_msgs/SetMode.h>
#include <mavros_msgs/State.h>
#include <geometry_msgs/TwistStamped.h>
#include <object_tracker/TrackerOffset.h>
#include "px4_tracker_control/velocity_generator.hpp"
#include "px4_tracker_control/basic_velocity_generator.hpp"

namespace px4_tracker_control {

class PX4TrackerControl {
public:
    PX4TrackerControl() : nh_("~") {
        initializeParameters();
        setupSubscribers();
        setupPublishers();
        setupServiceClients();
        initializeState();
        setupVelocityGenerator();
    }

    void run() {
        ros::Rate rate(20.0);
        
        // 等待FCU连接
        waitForFCUConnection(rate);
        
        // 初始化速度控制
        geometry_msgs::TwistStamped vel = initializeVelocityCommand();
        
        // 发送初始setpoints
        sendInitialSetpoints(vel, rate);
        
        // 设置OFFBOARD模式和解锁命令
        mavros_msgs::SetMode offb_set_mode;
        offb_set_mode.request.custom_mode = "OFFBOARD";
        
        mavros_msgs::CommandBool arm_cmd;
        arm_cmd.request.value = true;
        
        ros::Time last_request = ros::Time::now();
        
        // 主循环
        while(ros::ok()) {
            // 处理模式切换和解锁
            handleModeAndArming(offb_set_mode, arm_cmd, last_request);
            
            // 检查跟踪状态
            checkTrackingTimeout();
            
            // 更新速度命令
            vel = velocity_generator_->generateVelocity(tracker_offset_, tracking_started_);
            
            // 发布速度命令
            vel_pub_.publish(vel);
            
            ros::spinOnce();
            rate.sleep();
        }
    }

private:
    // 初始化函数
    void initializeParameters() {
        // 跟踪超时参数
        tracking_timeout_ = 1.0;  // 1秒没有跟踪信息就认为丢失
        last_tracking_time_ = ros::Time::now();
        
        // 设置起飞高度
        takeoff_height_ = 2.0;  // 米
    }
    
    void setupSubscribers() {
        state_sub_ = nh_.subscribe<mavros_msgs::State>
            ("mavros/state", 10, &PX4TrackerControl::stateCallback);
        tracker_offset_sub_ = nh_.subscribe<object_tracker::TrackerOffset>
            ("/tracker_offset", 10, &PX4TrackerControl::trackerOffsetCallback);
    }
    
    void setupPublishers() {
        vel_pub_ = nh_.advertise<geometry_msgs::TwistStamped>
            ("mavros/setpoint_velocity/cmd_vel", 10);
    }
    
    void setupServiceClients() {
        arming_client_ = nh_.serviceClient<mavros_msgs::CommandBool>
            ("mavros/cmd/arming");
        set_mode_client_ = nh_.serviceClient<mavros_msgs::SetMode>
            ("mavros/set_mode");
    }
    
    void initializeState() {
        current_state_.mode = "LAND";
        current_state_.armed = false;
        tracking_started_ = false;
    }
    
    void setupVelocityGenerator() {
        // 从参数服务器获取速度控制参数
        double max_velocity, velocity_scale;
        nh_.param("max_velocity", max_velocity, 1.0);
        nh_.param("velocity_scale", velocity_scale, 0.5);
        
        // 创建速度生成器
        velocity_generator_ = std::make_unique<BasicVelocityGenerator>(max_velocity, velocity_scale);
    }
    
    // 运行相关函数
    void waitForFCUConnection(ros::Rate& rate) {
        while(ros::ok() && !current_state_.connected) {
            ros::spinOnce();
            rate.sleep();
        }
    }
    
    geometry_msgs::TwistStamped initializeVelocityCommand() {
        geometry_msgs::TwistStamped vel;
        vel.twist.linear.x = 0;
        vel.twist.linear.y = 0;
        vel.twist.linear.z = 0;
        vel.twist.angular.x = 0;
        vel.twist.angular.y = 0;
        vel.twist.angular.z = 0;
        return vel;
    }
    
    void sendInitialSetpoints(geometry_msgs::TwistStamped& vel, ros::Rate& rate) {
        for(int i = 100; ros::ok() && i > 0; --i) {
            vel_pub_.publish(vel);
            ros::spinOnce();
            rate.sleep();
        }
    }
    
    void handleModeAndArming(mavros_msgs::SetMode& offb_set_mode, 
                            mavros_msgs::CommandBool& arm_cmd,
                            ros::Time& last_request) {
        if(current_state_.mode != "OFFBOARD" &&
           (ros::Time::now() - last_request > ros::Duration(5.0))) {
            if(set_mode_client_.call(offb_set_mode) &&
               offb_set_mode.response.mode_sent) {
                ROS_INFO("Offboard enabled");
            }
            last_request = ros::Time::now();
        } else if(current_state_.mode == "OFFBOARD" && !current_state_.armed &&
                 (ros::Time::now() - last_request > ros::Duration(5.0))) {
            if(arming_client_.call(arm_cmd) &&
               arm_cmd.response.success) {
                ROS_INFO("Vehicle armed");
            }
            last_request = ros::Time::now();
        }
    }
    
    void checkTrackingTimeout() {
        if(tracking_started_ && 
           (ros::Time::now() - last_tracking_time_ > ros::Duration(tracking_timeout_))) {
            tracking_started_ = false;
            ROS_WARN("Tracking timeout - no tracking information received for %.1f seconds", 
                    tracking_timeout_);
        }
    }
    
    // 回调函数
    void stateCallback(const mavros_msgs::State::ConstPtr& msg) {
        current_state_ = *msg;
    }
    
    void trackerOffsetCallback(const object_tracker::TrackerOffset::ConstPtr& msg) {
        tracker_offset_ = *msg;
        tracking_started_ = true;
        last_tracking_time_ = ros::Time::now();
    }
    
    // 成员变量
    ros::NodeHandle nh_;
    ros::Subscriber state_sub_;
    ros::Subscriber tracker_offset_sub_;
    ros::Publisher vel_pub_;
    ros::ServiceClient arming_client_;
    ros::ServiceClient set_mode_client_;
    
    mavros_msgs::State current_state_;
    object_tracker::TrackerOffset tracker_offset_;
    bool tracking_started_;
    double takeoff_height_;
    
    // 跟踪超时参数
    double tracking_timeout_;
    ros::Time last_tracking_time_;
    
    // 速度生成器
    std::unique_ptr<VelocityGenerator> velocity_generator_;
};

} // namespace px4_tracker_control

int main(int argc, char **argv) {
    ros::init(argc, argv, "px4_tracker_control_node");
    px4_tracker_control::PX4TrackerControl controller;
    controller.run();
    return 0;
} 