#ifndef BASIC_VELOCITY_GENERATOR_HPP
#define BASIC_VELOCITY_GENERATOR_HPP

#include "velocity_generator.hpp"

namespace px4_tracker_control {

class BasicVelocityGenerator : public VelocityGenerator {
public:
    BasicVelocityGenerator(double max_velocity = 1.0, double velocity_scale = 0.5)
        : max_velocity_(max_velocity), velocity_scale_(velocity_scale) {}
    
    geometry_msgs::TwistStamped generateVelocity(
        const object_tracker::TrackerOffset& tracker_offset,
        bool tracking_started) override {
        
        geometry_msgs::TwistStamped vel;
        vel.twist.linear.x = 0;
        vel.twist.linear.y = 0;
        vel.twist.linear.z = 0;
        vel.twist.angular.x = 0;
        vel.twist.angular.y = 0;
        vel.twist.angular.z = 0;
        
        if(tracking_started) {
            // 根据跟踪偏移量计算速度
            double vx = tracker_offset.offset_x * velocity_scale_;
            double vy = tracker_offset.offset_y * velocity_scale_;
            
            // 限制最大速度
            double speed = std::sqrt(vx * vx + vy * vy);
            if(speed > max_velocity_) {
                vx = vx / speed * max_velocity_;
                vy = vy / speed * max_velocity_;
            }
            
            vel.twist.linear.x = vx;
            vel.twist.linear.y = vy;
        }
        
        return vel;
    }
    
    void setMaxVelocity(double max_velocity) override {
        max_velocity_ = max_velocity;
    }
    
    void setVelocityScale(double scale) override {
        velocity_scale_ = scale;
    }
    
private:
    double max_velocity_;
    double velocity_scale_;
};

} // namespace px4_tracker_control

#endif // BASIC_VELOCITY_GENERATOR_HPP 