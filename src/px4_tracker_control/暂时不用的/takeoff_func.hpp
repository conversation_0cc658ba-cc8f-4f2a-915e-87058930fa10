#ifndef TAKEOFF_FUNC_HPP
#define TAKEOFF_FUNC_HPP

#include <ros/ros.h>
#include <mavros_msgs/CommandBool.h>
#include <mavros_msgs/SetMode.h>
#include <mavros_msgs/State.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <sensor_msgs/Image.h>
#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>

class TakeoffFunc {
public:
    // 构造函数
    TakeoffFunc();

    // 进入Offboard模式并解锁
    bool enterOffboardAndArm();

    // 起飞到指定高度
    bool takeoff(float speed, float time);

private:
    // ROS节点句柄
    ros::NodeHandle nh_;
    
    // 服务客户端
    ros::ServiceClient set_mode_client_;
    ros::ServiceClient arming_client_;
    
    // 发布器
    ros::Publisher vel_pub_;
    
    // 订阅器
    ros::Subscriber state_sub_;
    
    // 目标偏移量
    double target_offset_x_;
    double target_offset_y_;

    // 实时状态信息
    mavros_msgs::State current_state_;

    // 发布频率
    ros::Rate rate_;

    // 回调函数
    void stateCallback(const mavros_msgs::State::ConstPtr& msg);
};

#endif // TAKEOFF_FUNC_HPP