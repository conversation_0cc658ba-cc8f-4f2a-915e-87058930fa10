#include "tracker_func.hpp" 
#include "takeoff_func.hpp"

/*  
    主函数
    ObjectTracker 跟踪目标类，只提供跟踪目标功能，输入图像数据，输出跟踪结果及偏差
    TakeoffFunc 起飞类，只提供起飞功能，输入起飞速度和起飞时间，输出起飞结果
    1. 进入Offboard模式并解锁
    2. 起飞速度为1.0，持续5秒
    3. 开始跟踪目标
*/

int main(int argc, char **argv) {
    ros::init(argc, argv, "tacker_node");
    TakeoffFunc takeoff;
    ObjectTracker tracker;

    // 步骤1：进入Offboard模式并解锁
    if (!takeoff.enterOffboardAndArm()) {
        ROS_ERROR("无法进入Offboard模式或解锁！");
        return -1;
    }

    // 步骤2：起飞速度为1.0，持续5秒
    if (!takeoff.takeoff(1.0, 5.0)) {
        ROS_ERROR("起飞失败！");
        return -1;
    }

    // 步骤3：开始跟踪目标
    tracker.trackTarget();

    return 0;
}


int main() {
    // 创建跟踪器实例（使用默认的SiamRPN跟踪器）
    ObjectTracker tracker;
    
    // 设置图像尺寸（可选，如果与默认值不同）
    tracker.setImageSize(640, 480);
    
    // 处理整个图像序列
    tracker.processImageSequence("debayer");
    
    // 或者手动处理单个图像
    /*
    cv::Mat frame = cv::imread("image.png");
    if (!tracker.getTrackingStatus()) {
        tracker.initializeTracker(frame);
    } else {
        tracker.updateTracker(frame);
    }
    tracker.displayResult(frame);
    */
    
    cv::waitKey(0);
    return 0;
}