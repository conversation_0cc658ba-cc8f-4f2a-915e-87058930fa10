#!/usr/bin/env python3
import rospy
import numpy as np
from geometry_msgs.msg import TwistStamped, PoseStamped, Point
from mavros_msgs.msg import State
from mavros_msgs.srv import SetMode, CommandBool
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import cv2

class TargetTracker:
    def __init__(self):
        # 初始化ROS节点
        rospy.init_node('target_tracker', anonymous=True)
        
        # MAVROS接口初始化
        self.state = State()
        self.target_pose = PoseStamped()
        self.cv_bridge = CvBridge()
        
        # 控制器参数
        self.Kp = 0.02  # 比例控制系数
        self.target_lost = True
        
        # 订阅者
        self.state_sub = rospy.Subscriber('/uav0/mavros/state', State, self.state_cb)
        self.image_sub = rospy.Subscriber('/uav0/camera/image_raw', Image, self.image_cb)
        
        # 发布者
        self.vel_pub = rospy.Publisher('/uav0/mavros/setpoint_velocity/cmd_vel', TwistStamped, queue_size=10)
        self.local_pos_pub = rospy.Publisher('/uav0/mavros/setpoint_position/local', PoseStamped, queue_size=10)
        
        # 服务客户端
        rospy.wait_for_service('/uav0/mavros/cmd/arming')
        self.arming_client = rospy.ServiceProxy('/uav0/mavros/cmd/arming', CommandBool)
        rospy.wait_for_service('/uav0/mavros/set_mode')
        self.set_mode_client = rospy.ServiceProxy('/uav0/mavros/set_mode', SetMode)

    def state_cb(self, msg):
        self.state = msg

    def image_cb(self, msg):
        try:
            # 转换ROS图像消息到OpenCV格式
            cv_image = self.cv_bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # 目标检测逻辑（示例使用颜色阈值）
            hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
            lower_red = np.array([0, 100, 100])
            upper_red = np.array([10, 255, 255])
            mask = cv2.inRange(hsv, lower_red, upper_red)
            
            # 计算目标中心
            M = cv2.moments(mask)
            if M["m00"] > 0:
                self.target_lost = False
                cx = int(M["m10"]/M["m00"])
                cy = int(M["m01"]/M["m00"])
                self.calculate_velocity(cx, cy, cv_image.shape)
            else:
                self.target_lost = True
                
        except Exception as e:
            rospy.logerr(e)

    def calculate_velocity(self, x, y, shape):
        # 计算图像中心与目标偏移量
        img_height, img_width = shape[:2]
        center_x = img_width // 2
        center_y = img_height // 2
        
        # 生成速度指令
        vel_cmd = TwistStamped()
        vel_cmd.header.stamp = rospy.Time.now()
        
        # X方向速度（左右平移）
        vel_cmd.twist.linear.y = (center_x - x) * self.Kp
        # Z方向速度（高度调整）
        vel_cmd.twist.linear.z = (center_y - y) * self.Kp
        
        self.vel_pub.publish(vel_cmd)

    def set_mode(self, mode):
        rate = rospy.Rate(2)
        while not rospy.is_shutdown() and self.state.mode != mode:
            self.set_mode_client(0, mode)
            rate.sleep()

    def arm(self):
        while not rospy.is_shutdown() and not self.state.armed:
            self.arming_client(True)
            rospy.sleep(0.2)

    def takeoff(self, height):
        pose = PoseStamped()
        pose.pose.position.z = height
        
        # 持续发布设定点
        rate = rospy.Rate(20)
        for _ in range(100):
            self.local_pos_pub.publish(pose)
            rate.sleep()
        
        # 执行起飞
        while not rospy.is_shutdown():
            current_z = self.state._position.z  # 需要实际位置订阅
            if current_z >= height - 0.2:
                break
            self.local_pos_pub.publish(pose)
            rate.sleep()

    def run(self):
        # 等待连接
        while not rospy.is_shutdown() and not self.state.connected:
            rospy.sleep(0.1)
        
        # 进入OFFBOARD模式
        self.set_mode("OFFBOARD")
        self.arm()
        self.takeoff(2.0)
        
        # 主控制循环
        rate = rospy.Rate(20)
        while not rospy.is_shutdown():
            if self.target_lost:
                # 目标丢失时悬停
                self.vel_pub.publish(TwistStamped())
            rate.sleep()

if __name__ == '__main__':
    try:
        tracker = TargetTracker()
        tracker.run()
    except rospy.ROSInterruptException:
        pass
