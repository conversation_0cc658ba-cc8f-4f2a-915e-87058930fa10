<?xml version="1.0"?>
<launch>
    <!-- 其他参数 -->
    <arg name="tracker_id" default="4"/>  <!-- 添加跟踪器ID参数 -->
    
    <!-- 启动C++版本的object_tracker节点 -->
    <!-- <node name="object_tracker_node" pkg="object_tracker" type="object_tracker_node" output="screen"> -->
        <!-- 图像话题参数 -->
        <!-- <param name="image_topic" value="/iris_depth_camera_0/camera/rgb/image_raw" /> -->
        
        <!-- 图像尺寸参数 -->
        <!-- <param name="image_width" value="848" /> -->
        <!-- <param name="image_height" value="480" /> -->
        
        <!-- 输出话题 -->
        <!-- <remap from="tracker_offset" to="/tracker_offset" /> -->
        
        <!-- 其他参数: 设置跟踪器ID参数-->
        <!-- <param name="tracker_id" value="$(arg tracker_id)"/>   -->
    <!-- </node> -->

    <!-- 启动Python版本的object_tracker节点 -->
    <!-- <node name="object_tracker_py_node" pkg="object_tracker" type="object_tracker_GRM_node.py" output="screen"> -->
        <!-- 图像话题参数 -->
        <!-- <param name="image_topic" value="/iris_depth_camera_0/camera/rgb/image_raw" /> -->
        
        <!-- 图像尺寸参数 -->
        <!-- <param name="image_width" value="848" /> -->
        <!-- <param name="image_height" value="480" /> -->
        
        <!-- 输出话题 -->
        <!-- <remap from="tracker_offset" to="/tracker_offset" /> -->
        
        <!-- 其他参数：设置跟踪器ID参数-->
        <!-- <param name="tracker_id" value="$(arg tracker_id)"/>   -->
    <!-- </node> -->



    <!-- <node name="object_tracker_py_node" pkg="object_tracker" type="object_tracker_mixformer_node.py" output="screen">

        <param name="image_topic" value="/iris_depth_camera_0/camera/rgb/image_raw" />
        
        <param name="image_width" value="848" />
        <param name="image_height" value="480" />
        
        <remap from="tracker_offset" to="/tracker_offset" />  -->
        <!-- 设置跟踪器ID参数 -->
        <!-- <param name="tracker_id" value="$(arg tracker_id)"/>  
    </node>  -->

    <node name="object_tracker_py_node" pkg="object_tracker" type="object_tracker_mcitrack_node.py" output="screen">

        <param name="image_topic" value="/iris_depth_camera_0/camera/rgb/image_raw" />
        
        <param name="image_width" value="848" />
        <param name="image_height" value="480" />
        
        <remap from="tracker_offset" to="/tracker_offset" />
        <!-- 设置跟踪器ID参数 -->
        <param name="tracker_id" value="$(arg tracker_id)"/>  
    </node> 
</launch> 