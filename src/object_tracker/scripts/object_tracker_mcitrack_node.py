#!/home/<USER>/miniconda3/envs/mcitrack/bin/python3.11
import sys
import os
import rospy
import importlib
import traceback

print("Starting object_tracker_mcitrack_node.py")


# 添加mcitrack工程路径
mcitrack_project_path = "/home/<USER>/xb/MCITracker"  # mcitrack工程路径
if mcitrack_project_path not in sys.path:
    sys.path.append(mcitrack_project_path)
print(f"Python path: {sys.path}")

import rospy
from base_tracker_node import BaseTrackerNode

from lib.test.tracker.mcitrack import MCITRACK


class ObjectTrackerNode(BaseTrackerNode):
    def __init__(self):
        """
        初始化对象跟踪节点
        """
        super().__init__(node_name='object_tracker_mcitrack_node')
        
        # 获取跟踪器ID参数
        self.tracker_id = rospy.get_param('tracker_id', 4)
    
    def _get_tracker(self):
        """
        获取跟踪器实例
        :return: 跟踪器实例
        """
        print("Entering _get_tracker method")
        try:
            # 参数设置
            tracker_name = 'mcitrack'
            tracker_cfg = 'mcitrack_b224'  # 自定义选择cfg结构，可选为mcitrack_b224、mcitrack_l224、mcitrack_l384、mcitrack_s224、mcitrack_t224
            dataset = 'lasot'  # 可选'name of dataset (otb, nfs, uav, tpl, vot, tn, gott, gotv, lasot)'，主要用来获得超参数阈值


            print(f"Trying to import module: lib.test.parameter.{tracker_name}")
            # 获得cfg
            try:
                param_module = importlib.import_module('lib.test.parameter.{}'.format(tracker_name))
                print("Successfully imported parameter module")
            except ImportError as e:
                print(f"Failed to import parameter module: {str(e)}")
                traceback.print_exc()
                raise


            # 获取参数
            try:
                print("Getting parameters...")
                cfg_params = param_module.parameters(tracker_cfg)
                print("Successfully got parameters")
                print(f"Parameters: {cfg_params}")
                rospy.loginfo(f"Using cfg_params: {cfg_params}")
            except Exception as e:
                print(f"Failed to get parameters: {str(e)}")
                traceback.print_exc()
                raise

            cfg_params.debug = False

            # 创建跟踪器实例
            try:
                print("Creating MCITrack tracker instance")
                # 确保使用CPU
                print("Device before creating tracker: CPU (forced)")

                # 添加超时机制
                import threading
                import time

                def create_tracker():
                    nonlocal tracker_created, tracker
                    try:
                        tracker = MCITRACK(cfg_params, dataset)
                        tracker_created = True
                        print("MCITrack tracker created successfully")
                    except Exception as e:
                        print(f"Exception in create_tracker thread: {str(e)}")
                        traceback.print_exc()

                tracker = None
                tracker_created = False

                # 创建线程
                tracker_thread = threading.Thread(target=create_tracker)
                tracker_thread.daemon = True
                tracker_thread.start()

                # 等待线程完成，最多等待30秒
                timeout = 30
                start_time = time.time()
                while not tracker_created and time.time() - start_time < timeout:
                    print(f"Waiting for tracker creation... ({int(time.time() - start_time)}s)")
                    time.sleep(1)

                if not tracker_created:
                    print(f"Tracker creation timed out after {timeout} seconds")
                    raise TimeoutError(f"Tracker creation timed out after {timeout} seconds")

                print("MCITrack tracker created successfully")
                rospy.loginfo("MCITrack tracker created successfully")
                return tracker
            except Exception as e:
                print(f"Failed to create tracker instance: {str(e)}")
                traceback.print_exc()
                raise

        except Exception as e:
            print(f"Error in _get_tracker: {str(e)}")
            traceback.print_exc()
            # 返回一个简单的跟踪器实例，避免None值
            class DummyTracker:
                def initialize(self, image, info):
                    print("Using dummy tracker!")
                    return
                def track(self, image):
                    return {'target_bbox': [0, 0, 0, 0]}
            return DummyTracker()
    
    def _init_tracker(self, cv_image, bbox):
        """
        初始化跟踪器
        :param cv_image: OpenCV格式的图像
        :param bbox: 初始化边界框 (x, y, width, height)
        :return: 初始化是否成功
        """
        """
        初始化跟踪器
        :param cv_image: OpenCV格式的图像
        :param bbox: 初始化边界框 (x, y, width, height)
        :return: 初始化是否成功
        """
        try:
            bbox_list = list(bbox)
            rospy.loginfo(f"Initializing MixFormer tracker with bbox: {bbox_list}")

            init_info = {'init_bbox': bbox_list}
            # 初始化跟踪器
            self.tracker.initialize(cv_image, init_info)
            return True

        except Exception as e:
            rospy.logerr(f"Failed to initialize MixFormer tracker: {str(e)}")
            return False

    
    def _update_tracker(self, cv_image):
        """
        更新跟踪器
        :param cv_image: OpenCV格式的图像
        :return: 更新是否成功
        """
        try:
            results = self.tracker.track(cv_image)
            [x, y, w, h] = results['target_bbox']
            return x, y, w, h
        except Exception as e:
            rospy.logerr(f"Error in MixFormer tracking: {str(e)}")
            return None, None, None, None
      
    

def main():
    try:
        node = ObjectTrackerNode()
        node.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()