#!/home/<USER>/miniconda3/envs/grm/bin/python3.9
import sys
import os
import rospy
import importlib

# 添加其他工程的路径
other_project_path = "/home/<USER>/track/code/GRM"  # 替换为实际的工程路径
if other_project_path not in sys.path:
    sys.path.append(other_project_path)

import rospy
from base_tracker_node import BaseTrackerNode
from lib.test.tracker.grm import GRM

class ObjectTrackerNode(BaseTrackerNode):
    def __init__(self):
        """
        初始化对象跟踪节点
        """
        super().__init__(node_name='object_tracker_node')
        
        # 获取跟踪器ID参数
        self.tracker_id = rospy.get_param('tracker_id', 4)

    
    def _get_tracker(self):
        """
        获取跟踪器实例
        :return: 跟踪器实例
        """
          # 参数设置
        tracker_name='grm'
        tracker_cgf = 'vitb_256_ep300'
        dataset = 'lasot'   # 可选'name of dataset (otb, nfs, uav, tpl, vot, tn, gott, gotv, lasot)'，主要用来获得超参数阈值
        
        # 获得cgf
        param_module = importlib.import_module('lib.test.parameter.{}'.format(tracker_name))
        cfg_params = param_module.parameters(tracker_cgf)
        cfg_params.debug = False

        tracker = GRM(cfg_params,dataset)
        
        return tracker
    
    def _init_tracker(self, cv_image, bbox):
        """
        初始化跟踪器
        :param cv_image: OpenCV格式的图像
        :param bbox: 初始化边界框 (x, y, width, height)
        :return: 初始化是否成功
        """
        try:
            bbox_list = list(bbox)
            print(bbox_list,type(bbox_list))

            init_info={'init_bbox': bbox_list}
            # 初始化跟踪器
            self.tracker.initialize(cv_image,init_info)
            return True
            
        except Exception as e:
            rospy.logerr(f"Failed to initialize tracker: {str(e)}")
            return False
    
    def _update_tracker(self, cv_image):
        """
        更新跟踪器
        :param cv_image: OpenCV格式的图像
        :return: x,y,w,h   # 跟踪后的结果
        """
        results = self.tracker.track(cv_image)
        [x,y,w,h] = results['target_bbox']

        return x,y,w,h

           
    
   
def main():
    try:
        node = ObjectTrackerNode()
        node.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()