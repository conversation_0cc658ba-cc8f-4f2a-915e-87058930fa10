#!/usr/bin/env python3
import rospy
from base_tracker_node import BaseTrackerNode
from py_tracker_wrapper import mytracker

class ObjectTrackerNode(BaseTrackerNode):
    def __init__(self):
        """
        初始化对象跟踪节点
        """
        super().__init__(node_name='object_tracker_node')
        
        # 获取跟踪器ID参数
        self.tracker_id = rospy.get_param('tracker_id', 4)
    
    def _get_tracker(self):
        """
        获取跟踪器实例
        :return: 跟踪器实例
        """
        return mytracker("YourPythonTracker")
    
    def _init_tracker(self, cv_image, bbox):
        """
        初始化跟踪器
        :param cv_image: OpenCV格式的图像
        :param bbox: 初始化边界框 (x, y, width, height)
        :return: 初始化是否成功
        """
        try:
            # 设置图像尺寸
            self.tracker.setImageSize(self.image_width, self.image_height)
            # 初始化跟踪器
            return self.tracker.init(cv_image, bbox)
        except Exception as e:
            rospy.logerr(f"Failed to initialize tracker: {str(e)}")
            return False
    
    def _update_tracker(self, cv_image):
        """
        更新跟踪器
        :param cv_image: OpenCV格式的图像
        :return: 更新是否成功
        """
        x,y,w,h = self.tracker.update(cv_image)

        return x,y,w,h
      
    

def main():
    try:
        node = ObjectTrackerNode()
        node.run()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()