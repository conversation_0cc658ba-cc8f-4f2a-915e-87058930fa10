#!/home/<USER>/miniconda3/envs/mixformer/bin/python3.6
import rospy
import cv2
import numpy as np
import os
import datetime
import rospkg
from abc import ABC, abstractmethod
from sensor_msgs.msg import Image
from cv_bridge import CvBridge, CvBridgeError
from object_tracker.msg import TrackerOffset

class BaseTrackerNode(ABC):
    def __init__(self, node_name='base_tracker_node'):
        """
        初始化基类
        :param node_name: ROS节点名称
        """

        # 初始化ROS节点
        rospy.init_node(node_name, anonymous=True)

        # 获取参数
        self.image_width = rospy.get_param('image_width', 848)
        self.image_height = rospy.get_param('image_height', 480)
        self.image_topic = rospy.get_param('image_topic', '/iris_depth_camera_0/camera/rgb/image_raw')


        # 显示和保存图像相关参数
        self.display_images = rospy.get_param('display_images', True)  # 默认显示图像
        self.save_images = rospy.get_param('save_tracked_images', True)  # 默认保存图像
        self.save_dir = rospy.get_param('tracked_images_dir', rospkg.RosPack().get_path('object_tracker') + '/results')

        # 创建CV桥接器
        self.bridge = CvBridge()

        # 设置订阅器和发布器
        self.image_sub = rospy.Subscriber(self.image_topic, Image, self.image_callback)
        self.tracker_offset_pub = rospy.Publisher('/tracker_offset', TrackerOffset, queue_size=1)

        # 初始化变量
        self.delta_x = 0.0
        self.delta_y = 0.0
        self.pixels_num = 0

        # 初始化状态
        self.is_initialized = False
        self.init_bbox = None  # 用于存储初始化时的边界框

        self.currentBox_x=0
        self.currentBox_y=0
        self.currentBox_w=0
        self.currentBox_h=0

        # 获取跟踪器实例
        self.tracker = self._get_tracker()



    @abstractmethod
    def _get_tracker(self):
        """
        获取跟踪器实例，子类必须实现此方法
        :return: 跟踪器实例
        """
        pass

    @abstractmethod
    def _init_tracker(self, cv_image, bbox):
        """
        初始化跟踪器，子类必须实现此方法
        :param cv_image: OpenCV格式的图像
        :param bbox: 初始化边界框 (x, y, width, height)
        :return: 初始化是否成功
        """
        pass

    @abstractmethod
    def _update_tracker(self, cv_image):
        """
        更新跟踪器，子类必须实现此方法
        :param cv_image: OpenCV格式的图像
        :return: x,y,w,h   # 跟踪后的结果
        """
        pass

    def get_tracking_info(self):
        """
        获取跟踪信息，子类必须实现此方法
        :return: (delta_x, delta_y, pixels_num)
        """
        center_x = self.image_width / 2.0
        center_y = self.image_height / 2.0

        roi_center_x = self.currentBox_x + self.currentBox_w / 2
        roi_center_y = self.currentBox_y + self.currentBox_h / 2

        delta_x = roi_center_x - center_x
        delta_y = roi_center_y - center_y
        pixels_num = int(self.currentBox_w * self.currentBox_h)

        return delta_x,delta_y,pixels_num


    def process_image(self, cv_image):
        """
        处理图像的主函数
        :param cv_image: OpenCV格式的图像
        :return: 处理是否成功
        """
        if not self.is_initialized:
            # 如果未初始化，尝试进行初始化
            if self.init_bbox is None:   # 没有跟踪框，获取跟踪框
                self.init_bbox = cv2.selectROI("select the tracked object", cv_image)
                if (self.init_bbox[2] == 0 and self.init_bbox[3] == 0):   # 宽、高为0，代表没有选中
                    rospy.logwarn("NO ROI selected")
                    self.init_bbox = None
                    return False

            # 进行初始化
            initialized_success = self._init_tracker(cv_image, self.init_bbox)
            if initialized_success:
                self.is_initialized = True
                rospy.loginfo("Tracker initialized successfully")
            return False   ##### 初始化时，不算做跟踪成功
        else:
            # 已初始化，进行跟踪更新
            try:
                self.currentBox_x, self.currentBox_y, self.currentBox_w, self.currentBox_h = self._update_tracker(cv_image)
                return True
            except Exception as e:
                rospy.logerr(f"Failed to update tracker: {str(e)}")
                return False

    def image_callback(self, msg):
        """
        图像回调函数
        :param msg: ROS图像消息
        """
        try:
            # 转换图像消息为OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")

            # 处理图像
            tracking_success = self.process_image(cv_image)

            if tracking_success:
                # 获取跟踪信息
                self.delta_x, self.delta_y, self.pixels_num = self.get_tracking_info()

                # 打印调试信息
                rospy.loginfo("delta_x: %f, delta_y: %f, pixels_num: %d",
                            self.delta_x, self.delta_y, self.pixels_num)

                # 发布跟踪偏移量
                self._publish_offset()

                # 显示跟踪结果图像
                self._display_image(cv_image)

                # 保存跟踪后的图像
                self.save_tracked_image(cv_image)

        except CvBridgeError as e:
            rospy.logerr("cv_bridge exception: %s", str(e))
        except Exception as e:
            rospy.logerr("Error in image callback: %s", str(e))

    def _publish_offset(self):
        """
        发布跟踪偏移量
        """
        offset = TrackerOffset()
        offset.offset_x = self.delta_x
        offset.offset_y = self.delta_y
        offset.pixels_num = self.pixels_num
        offset.width = self.image_width
        offset.height = self.image_height
        self.tracker_offset_pub.publish(offset)

    def _display_image(self, cv_image):
        """
        显示跟踪结果图像
        :param cv_image: OpenCV格式的图像
        """
        if not self.display_images:
            return

        # 绘制跟踪框
        x, y, w, h = int(self.currentBox_x), int(self.currentBox_y), int(self.currentBox_w), int(self.currentBox_h)
        cv2.rectangle(cv_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

        # 显示图像
        cv2.imshow("Tracking Result", cv_image)
        # 等待1毫秒，允许图像窗口更新，同时保持程序响应
        cv2.waitKey(1)

    def save_tracked_image(self, cv_image):
        """
        保存跟踪后的图像
        :param cv_image: OpenCV格式的图像
        """
        if not self.save_images:
            return

        try:
            # 创建保存目录（如果不存在）
            if not os.path.exists(self.save_dir):
                os.makedirs(self.save_dir)

            # 生成带时间戳的文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = os.path.join(self.save_dir, f"tracked_image_{timestamp}.jpg")

            # 绘制跟踪框
            img_to_save = cv_image.copy()
            x, y, w, h = int(self.currentBox_x), int(self.currentBox_y), int(self.currentBox_w), int(self.currentBox_h)
            cv2.rectangle(img_to_save, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # 添加跟踪信息文本
            info_text = f"Offset: ({self.delta_x:.1f}, {self.delta_y:.1f})"
            cv2.putText(img_to_save, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

            # 保存图像
            cv2.imwrite(filename, img_to_save)
            rospy.loginfo(f"Saved tracked image to {filename}")

        except Exception as e:
            rospy.logerr(f"Failed to save tracked image: {str(e)}")


    def run(self):
        """
        运行节点
        """
        rospy.spin()

    def __del__(self):
        """
        析构函数
        """
        # 清理资源
        pass
        pass