#ifndef OBJECT_TRACKER_OBJECT_TRACKER_HPP
#define OBJECT_TRACKER_OBJECT_TRACKER_HPP

#include <opencv2/opencv.hpp>
#include <opencv2/tracking.hpp>
#include <string>
#include <tuple>

namespace object_tracker {

class ObjectTracker {
public:
    // 构造函数，初始化跟踪器类型
    explicit ObjectTracker(int trackerType = 4);  // 默认使用SiamRPN
    
    // 设置图像尺寸（用于计算偏移量）
    void setImageSize(int width, int height);
    
    // 初始化跟踪器（选择ROI）
    bool initializeTracker(const cv::Mat& frame);

    // 更新跟踪器
    bool updateTracker(const cv::Mat& frame);
    
    // 获取当前跟踪框
    cv::Rect getCurrentBBox() const;
    
    // 获取跟踪状态
    bool getTrackingStatus() const;
    
    // 获取目标相对于中心的偏移量和面积
    std::tuple<double, double, int> getTrackingInfo() const;
    
    // 显示跟踪结果
    void displayResult(const cv::Mat& frame);
    
    // 处理单个图像
    bool processSingleImage(const cv::Mat& frame);

private:
    // 内部辅助函数
    cv::Ptr<cv::Tracker> select_tracker(int trackerID);
    void drawRectangle(cv::Mat& frame, const cv::Rect& bbox) const;
    void drawText(cv::Mat& frame, const std::string& txt, 
                 const cv::Point& location, const cv::Scalar& color) const;
    
    // 成员变量
    cv::Ptr<cv::Tracker> tracker_;
    cv::Rect currentBBox_;
    bool isInitialized_;
    bool trackingOK_;
    int imageWidth_;
    int imageHeight_;
    std::vector<std::string> tracker_types_ = {
        "MIL", "KCF", "CSRT", "GOTURN", "SiamRPN", "Nano"
    };
};

} // namespace object_tracker

#endif // OBJECT_TRACKER_OBJECT_TRACKER_HPP 