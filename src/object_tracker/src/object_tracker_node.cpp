#include <ros/ros.h>
#include <sensor_msgs/Image.h>
#include <cv_bridge/cv_bridge.h>
#include "object_tracker/object_tracker.hpp"
#include "object_tracker/TrackerOffset.h"

class ObjectTrackerNode {
public:
    ObjectTrackerNode() : nh_("~") {
        
        
        // 获取参数
        std::string image_topic;
        int tracker_id;  // 添加跟踪器ID变量
        
        nh_.param("image_width", image_width_, 640);
        nh_.param("image_height", image_height_, 480);
        nh_.param<std::string>("image_topic", image_topic, "/iris_depth_camera_0/camera/rgb/image_raw");
        nh_.param("tracker_id", tracker_id, 4);  // 从launch文件加载跟踪器ID，默认为4
        
        // 创建跟踪器实例
        tracker_ = std::make_unique<object_tracker::ObjectTracker>(tracker_id);
        // 设置图像尺寸
        tracker_->setImageSize(image_width_, image_height_);
        
        // 设置订阅器和发布器
        image_sub_ = nh_.subscribe(image_topic, 1, 
                                 &ObjectTrackerNode::imageCallback, this);
        tracker_offset_pub_ = nh_.advertise<object_tracker::TrackerOffset>("/tracker_offset", 1);

    }

private:
    void imageCallback(const sensor_msgs::ImageConstPtr& msg) {
        try {
            cv_bridge::CvImagePtr cv_ptr = cv_bridge::toCvCopy(msg, "bgr8");
            bool tracking_success = tracker_->processSingleImage(cv_ptr->image);
            if (tracking_success) {
                // 发布跟踪偏移量
                object_tracker::TrackerOffset offset;
                std::tie(delta_x_, delta_y_, pixels_num_)= tracker_->getTrackingInfo();
                    ROS_INFO("delta_x: %f, delta_y: %f, pixels_num: %d", delta_x_, delta_y_, pixels_num_);
                    offset.offset_x = delta_x_;
                    offset.offset_y = delta_y_;
                    offset.pixels_num = pixels_num_;
                    offset.width = image_width_;
                    offset.height = image_height_;
                    tracker_offset_pub_.publish(offset);
            }
        } catch (cv_bridge::Exception& e) {
            ROS_ERROR("cv_bridge exception: %s", e.what());
        }
    }

    ros::NodeHandle nh_;
    ros::Subscriber image_sub_;
    ros::Publisher tracker_offset_pub_;
    std::unique_ptr<object_tracker::ObjectTracker> tracker_;
    double delta_x_, delta_y_;
    int pixels_num_;
    int image_width_, image_height_;
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "object_tracker_node");
    ObjectTrackerNode node;
    ros::spin();
    return 0;
}
