/**
 * @file object_tracker.cpp
 * @brief 基于 OpenCV 4.8.0 的目标跟踪器实现
 * 
 * 本文件实现了基于 OpenCV 4.8.0 的目标跟踪功能，支持多种跟踪算法：
 * - BOOSTING
 * - MIL
 * - KCF
 * - CSRT
 * - TLD
 * - MEDIANFLOW
 * - GOTURN
 * - MOSSE
 * - SiamRPN
 * - Nano
 * - Vit
 */

#include "object_tracker/object_tracker.hpp"
#include <ros/ros.h>
#include <ros/package.h>

namespace object_tracker {
ObjectTracker::ObjectTracker(int trackerType) 
    : isInitialized_(false)
    , trackingOK_(false)
    , imageWidth_(324)  // 默认值
    , imageHeight_(244) // 默认值
{
    tracker_ = select_tracker(trackerType);
}

void ObjectTracker::setImageSize(int width, int height) {
    imageWidth_ = width;
    imageHeight_ = height;
}

bool ObjectTracker::initializeTracker(const cv::Mat& frame) {
    currentBBox_ = cv::selectROI(frame);

    // TODO: 跟踪算法主要实现的地方，初始化跟踪器
    if (currentBBox_.width == 0 && currentBBox_.height == 0) {
        isInitialized_ = false;
        ROS_WARN("NO ROI selected");
        return false;
    }
    
    trackingOK_ = true;
    isInitialized_ = true;
    tracker_->init(frame, currentBBox_);
    // TODO: 跟踪算法主要实现的地方

    return isInitialized_;
}

bool ObjectTracker::updateTracker(const cv::Mat& frame) {
    if (!isInitialized_) {
        return false;
    }
    // TODO: 跟踪算法主要实现的地方，更新跟踪器
    trackingOK_ = tracker_->update(frame, currentBBox_);
    // TODO: 跟踪算法主要实现的地方

    return trackingOK_;
}

cv::Rect ObjectTracker::getCurrentBBox() const {
    return currentBBox_;
}

bool ObjectTracker::getTrackingStatus() const {
    return trackingOK_;
}

std::tuple<double, double, int> ObjectTracker::getTrackingInfo() const {
    double center_x = imageWidth_ / 2.0;
    double center_y = imageHeight_ / 2.0;

    int roi_center_x = currentBBox_.x + currentBBox_.width / 2;
    int roi_center_y = currentBBox_.y + currentBBox_.height / 2;

    double delta_x = roi_center_x - center_x;
    double delta_y = roi_center_y - center_y;
    int pixels_num = currentBBox_.width * currentBBox_.height;

    return std::make_tuple(delta_x, delta_y, pixels_num);
}

void ObjectTracker::displayResult(const cv::Mat& frame) {
    cv::Mat frameCopy = frame.clone();
    double delta_x, delta_y;
    int pixels_num;
    if (trackingOK_) {
        drawRectangle(frameCopy, currentBBox_);        
        
    } else {
        drawText(frameCopy, "Tracking failure detected", 
                cv::Point(80, 140), cv::Scalar(0, 0, 255));
    }
    cv::imshow("Tracker", frameCopy);
    // 保存跟踪结果图片
    std::string save_path = ros::package::getPath("object_tracker") + "/results/";
    std::string timestamp = std::to_string(ros::Time::now().toSec());
    std::string filename = save_path + "tracking_result_" + timestamp + ".jpg";
    cv::imwrite(filename, frameCopy);
    cv::waitKey(10);
}

// 跟踪算法处理单个图像
bool ObjectTracker::processSingleImage(const cv::Mat& frame) {
    if (frame.empty()) {
        ROS_ERROR("Frame is empty");
        return false;
    }

    if (!isInitialized_) {
        if (!initializeTracker(frame)) {
            return false;
        }
    } else {
        updateTracker(frame);
        ROS_INFO("Tracking...");
    }
    // 展示跟踪结果
    displayResult(frame);
    return true;
}

void ObjectTracker::drawRectangle(cv::Mat& frame, const cv::Rect& bbox) const {
    cv::rectangle(frame, bbox, cv::Scalar(255, 0, 0), 2, 1);
}

void ObjectTracker::drawText(cv::Mat& frame, const std::string& txt, 
                           const cv::Point& location, const cv::Scalar& color) const {
    cv::putText(frame, txt, location, cv::FONT_HERSHEY_SIMPLEX, 1, color, 3);
}

cv::Ptr<cv::Tracker> ObjectTracker::select_tracker(int trackerID) {
    

    std::string tracker_type = tracker_types_[trackerID];
    cv::Ptr<cv::Tracker> tracker;

    // 解释ros::package::getPath("object_tracker")路径获取原理：
    // 1.ros::package::getPath("object_tracker") 函数的工作方式是：
    // 首先尝试在 ROS_PACKAGE_PATH 中查找包
    // 如果找到多个位置，会按照以下优先级返回路径：
    // 首先返回 source 空间（src/object_tracker）
    // 如果 source 空间不存在，则返回 devel 空间（devel/share/object_tracker）
    // 如果 devel 空间不存在，则返回 install 空间（install/share/object_tracker）

    // 2.所以在当前代码的情况下：
    // 如果源代码目录（src/object_tracker）不存在
    // 那么 ros::package::getPath("object_tracker") 会返回 devel/share/object_tracker 的路径
    // 因此 models_path 就会指向 devel/share/object_tracker/models

    // 3.这就是为什么在 CMakeLists.txt 中我们使用：
    // file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/models
    //      DESTINATION ${CATKIN_DEVEL_PREFIX}/share/${PROJECT_NAME}
    //      FILES_MATCHING 
    //        PATTERN "*.onnx"
    //        PATTERN "*.prototxt"
    //        PATTERN "*.caffemodel"
    // )
    // 这段代码确保模型文件被复制到 devel/share/object_tracker/models 目录下。
    // 所以，如果您删除源代码目录，程序会正确地使用 devel/share/object_tracker/models 中的模型文件。
    // 这就是 ROS 包管理系统的设计，它允许我们在不同空间（source、devel、install）之间灵活切换。

    // 获取当前工作空间路径
    std::string package_path = ros::package::getPath("object_tracker");
    std::string models_path = package_path + "/models";
    
    // // 打印路径信息
    // std::cout << "Package path: " << package_path << std::endl;
    // std::cout << "Models path: " << models_path << std::endl;

    if (tracker_type == "MIL") {
        tracker = cv::TrackerMIL::create();
    } else if (tracker_type == "KCF") {
        tracker = cv::TrackerKCF::create();
    } else if (tracker_type == "CSRT") {
        tracker = cv::TrackerCSRT::create();   
    } else if (tracker_type == "GOTURN") {
        cv::TrackerGOTURN::Params params;
        params.modelTxt = models_path + "/goturn.prototxt";
        params.modelBin = models_path + "/goturn.caffemodel";
        tracker = cv::TrackerGOTURN::create(params);
    } else if (tracker_type == "SiamRPN") {
        cv::TrackerDaSiamRPN::Params params;
        params.model = models_path + "/dasiamrpn_model.onnx";
        params.kernel_cls1 = models_path + "/dasiamrpn_kernel_cls1.onnx";
        params.kernel_r1 = models_path + "/dasiamrpn_kernel_r1.onnx";
        tracker = cv::TrackerDaSiamRPN::create(params);
    } else if (tracker_type == "Nano") {
        cv::TrackerNano::Params params;
        params.backbone = models_path + "/nanotrack_backbone_sim.onnx";
        params.neckhead = models_path + "/nanotrack_head_sim.onnx";
        tracker = cv::TrackerNano::create(params);    
    }
    return tracker;
} 
} // namespace object_tracker


